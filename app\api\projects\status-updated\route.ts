import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ProjectStatusUpdate } from '@/lib/emails/projects/status-updated';
import type { ProjectStatusUpdateEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve, reject) => {
    let emailData: ProjectStatusUpdateEmailData;

    request
      .json()
      .then((data: ProjectStatusUpdateEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.project ||
          !emailData.oldStatus ||
          !emailData.newStatus ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: project, oldStatus, newStatus, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = ProjectStatusUpdate(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(ProjectStatusUpdate(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Project Status Updated: ${emailData.project.name}`,
          react: ProjectStatusUpdate(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Project status update email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Project Status Updated: ${emailData.project.name}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Project status update email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error:
              error.message || 'Failed to send project status update email',
          })
        );
      });
  });
}
