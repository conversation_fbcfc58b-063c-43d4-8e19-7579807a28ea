'use client';

import { Plus, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { ClientsMetrics } from '@/components/database/clients-metrics';
import { ClientsDataTable } from '@/components/tables/database/clients-data-table';
import { Button } from '@/components/ui/button';
import { useClients } from '@/hooks/use-db';

export default function DatabaseClientsPage() {
  const { clients, loading } = useClients();

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center space-x-4'>
          <Link href='/admin/database'>
            <Button variant='ghost' size='sm' className='h-8'>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Database
            </Button>
          </Link>
          <p className='font-medium text-sm'>Database - Clients</p>
        </div>
        <Link href='/admin/clients'>
          <Button size='sm' className='h-8'>
            <Plus className='h-4 w-4 mr-2' />
            Add Client
          </Button>
        </Link>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <ClientsMetrics clients={clients} loading={loading} />
        <ClientsDataTable />
      </section>
    </main>
  );
}
