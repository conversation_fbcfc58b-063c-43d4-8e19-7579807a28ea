'use client';

import { Calendar, Edit, ExternalLink, Plus, Save, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useProfile } from '@/hooks/use-db';

interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  technologies: string[];
  link?: string;
  image?: string;
  date: string;
}

interface VolunteerSkill {
  id: string;
  name: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
  category: string;
}

export default function VolunteerPortfolioPage() {
  const { profile } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Mock data - in real implementation, this would come from database
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([
    {
      id: '1',
      title: 'Community Website Redesign',
      description: 'Led the redesign of a local nonprofit website, improving user experience and accessibility.',
      technologies: ['React', 'TypeScript', 'Tailwind CSS'],
      link: 'https://example.com',
      date: '2024-01-15',
    },
    {
      id: '2',
      title: 'Volunteer Management System',
      description: 'Developed a system to help organizations manage volunteer schedules and tasks.',
      technologies: ['Next.js', 'Supabase', 'PostgreSQL'],
      date: '2023-11-20',
    },
  ]);

  const [skills, setSkills] = useState<VolunteerSkill[]>([
    { id: '1', name: 'React', level: 'Advanced', category: 'Frontend' },
    { id: '2', name: 'TypeScript', level: 'Intermediate', category: 'Programming' },
    { id: '3', name: 'UI/UX Design', level: 'Intermediate', category: 'Design' },
    { id: '4', name: 'Project Management', level: 'Advanced', category: 'Management' },
  ]);

  const [bio, setBio] = useState(
    'Passionate volunteer developer with 3+ years of experience in web development. I love contributing to meaningful projects that make a positive impact in the community.'
  );

  const [editBio, setEditBio] = useState(bio);

  const handleSaveBio = () => {
    setIsUpdating(true);
    // Simulate API call
    setTimeout(() => {
      setBio(editBio);
      setIsEditing(false);
      setIsUpdating(false);
      toast.success('Portfolio updated successfully');
    }, 1000);
  };

  const handleCancelEdit = () => {
    setEditBio(bio);
    setIsEditing(false);
  };

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'Beginner':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'Intermediate':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'Advanced':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'Expert':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Portfolio</p>
        <div className='flex items-center gap-2'>
          {isEditing ? (
            <>
              <Button
                variant='ghost'
                size='sm'
                onClick={handleCancelEdit}
                disabled={isUpdating}
              >
                <X className='h-4 w-4' />
                Cancel
              </Button>
              <Button
                size='sm'
                onClick={handleSaveBio}
                disabled={isUpdating}
              >
                <Save className='h-4 w-4' />
                {isUpdating ? 'Saving...' : 'Save'}
              </Button>
            </>
          ) : (
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setIsEditing(true)}
            >
              <Edit className='h-4 w-4' />
              Edit
            </Button>
          )}
        </div>
      </header>
      
      <ScrollArea className='flex-1'>
        <section className='p-6 space-y-6'>
          {/* Profile Header */}
          <Card>
            <CardContent className='pt-6'>
              <div className='flex items-start gap-6'>
                <Avatar className='h-24 w-24'>
                  <AvatarImage 
                    src={profile?.avatar_url || ''} 
                    alt={profile?.full_name || 'Volunteer'} 
                  />
                  <AvatarFallback className='text-2xl'>
                    {profile?.full_name?.split(' ').map(n => n[0]).join('') || 'V'}
                  </AvatarFallback>
                </Avatar>
                <div className='flex-1 space-y-4'>
                  <div>
                    <h1 className='text-2xl font-bold'>{profile?.full_name || 'Volunteer'}</h1>
                    <p className='text-muted-foreground'>{profile?.email}</p>
                    <Badge variant='secondary' className='mt-2'>
                      {profile?.role || 'Volunteer'}
                    </Badge>
                  </div>
                  <div>
                    <Label className='text-sm font-medium'>Bio</Label>
                    {isEditing ? (
                      <Textarea
                        value={editBio}
                        onChange={(e) => setEditBio(e.target.value)}
                        className='mt-2 min-h-[100px]'
                        placeholder='Tell us about yourself and your volunteer experience...'
                      />
                    ) : (
                      <p className='mt-2 text-sm text-muted-foreground'>
                        {bio}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skills Section */}
          <Card>
            <CardHeader className='flex flex-row items-center justify-between'>
              <CardTitle>Skills & Expertise</CardTitle>
              <Button variant='outline' size='sm'>
                <Plus className='h-4 w-4 mr-2' />
                Add Skill
              </Button>
            </CardHeader>
            <CardContent>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {skills.map((skill) => (
                  <div key={skill.id} className='flex items-center justify-between p-3 border rounded-lg'>
                    <div>
                      <p className='font-medium'>{skill.name}</p>
                      <p className='text-sm text-muted-foreground'>{skill.category}</p>
                    </div>
                    <Badge className={getSkillLevelColor(skill.level)}>
                      {skill.level}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Portfolio Projects */}
          <Card>
            <CardHeader className='flex flex-row items-center justify-between'>
              <CardTitle>Portfolio Projects</CardTitle>
              <Button variant='outline' size='sm'>
                <Plus className='h-4 w-4 mr-2' />
                Add Project
              </Button>
            </CardHeader>
            <CardContent className='space-y-6'>
              {portfolioItems.map((item, index) => (
                <div key={item.id}>
                  <div className='space-y-4'>
                    <div className='flex items-start justify-between'>
                      <div className='flex-1'>
                        <div className='flex items-center gap-2'>
                          <h3 className='text-lg font-semibold'>{item.title}</h3>
                          {item.link && (
                            <Button variant='ghost' size='sm' asChild>
                              <a href={item.link} target='_blank' rel='noopener noreferrer'>
                                <ExternalLink className='h-4 w-4' />
                              </a>
                            </Button>
                          )}
                        </div>
                        <div className='flex items-center gap-2 mt-1 text-sm text-muted-foreground'>
                          <Calendar className='h-4 w-4' />
                          {new Date(item.date).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    
                    <p className='text-muted-foreground'>{item.description}</p>
                    
                    <div className='flex flex-wrap gap-2'>
                      {item.technologies.map((tech) => (
                        <Badge key={tech} variant='outline'>
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  {index < portfolioItems.length - 1 && <Separator className='mt-6' />}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Volunteer History */}
          <Card>
            <CardHeader>
              <CardTitle>Volunteer History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center justify-between p-4 border rounded-lg'>
                  <div>
                    <p className='font-medium'>Active Volunteer</p>
                    <p className='text-sm text-muted-foreground'>
                      Member since {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Unknown'}
                    </p>
                  </div>
                  <Badge variant='secondary'>Current</Badge>
                </div>
                
                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-6'>
                  <div className='text-center p-4 border rounded-lg'>
                    <p className='text-2xl font-bold text-primary'>12</p>
                    <p className='text-sm text-muted-foreground'>Projects Contributed</p>
                  </div>
                  <div className='text-center p-4 border rounded-lg'>
                    <p className='text-2xl font-bold text-primary'>48</p>
                    <p className='text-sm text-muted-foreground'>Issues Resolved</p>
                  </div>
                  <div className='text-center p-4 border rounded-lg'>
                    <p className='text-2xl font-bold text-primary'>156</p>
                    <p className='text-sm text-muted-foreground'>Hours Volunteered</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </ScrollArea>
    </main>
  );
}
