'use client';

import { <PERSON><PERSON><PERSON>, CheckCircle, Clock, Download, FileText, Users } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

export default function VolunteerBriefPage() {
  const [hasDownloaded, setHasDownloaded] = useState(false);

  const handleDownloadBrief = () => {
    // In real implementation, this would download the actual brief document
    setHasDownloaded(true);
    toast.success('Volunteer brief downloaded successfully');
  };

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-2'>
          <BookOpen className='h-4 w-4' />
          <p className='font-medium text-sm'>Volunteer Brief</p>
        </div>
        <Button variant='outline' size='sm' onClick={handleDownloadBrief}>
          <Download className='h-4 w-4 mr-2' />
          Download PDF
        </Button>
      </header>
      
      <ScrollArea className='flex-1'>
        <section className='p-6 space-y-6'>
          {/* Welcome Section */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='h-5 w-5' />
                Welcome to TheHueFactory Volunteer Program
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <p className='text-muted-foreground'>
                Thank you for your interest in volunteering with TheHueFactory! This brief contains 
                everything you need to know about our volunteer program, expectations, and how to 
                get started contributing to meaningful projects.
              </p>
              
              <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-6'>
                <div className='text-center p-4 border rounded-lg'>
                  <Users className='h-8 w-8 text-primary mx-auto mb-2' />
                  <p className='font-medium'>Community Driven</p>
                  <p className='text-sm text-muted-foreground'>Join our growing community of volunteers</p>
                </div>
                <div className='text-center p-4 border rounded-lg'>
                  <FileText className='h-8 w-8 text-primary mx-auto mb-2' />
                  <p className='font-medium'>Meaningful Projects</p>
                  <p className='text-sm text-muted-foreground'>Work on projects that make a difference</p>
                </div>
                <div className='text-center p-4 border rounded-lg'>
                  <CheckCircle className='h-8 w-8 text-primary mx-auto mb-2' />
                  <p className='font-medium'>Skill Development</p>
                  <p className='text-sm text-muted-foreground'>Grow your skills while helping others</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Our Mission */}
          <Card>
            <CardHeader>
              <CardTitle>Our Mission</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <p className='text-muted-foreground'>
                TheHueFactory is dedicated to creating innovative digital solutions that empower 
                organizations and communities. Our volunteer program connects passionate individuals 
                with meaningful projects that create positive social impact.
              </p>
              
              <div className='space-y-3'>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <div>
                    <p className='font-medium'>Empowerment Through Technology</p>
                    <p className='text-sm text-muted-foreground'>
                      We believe technology should be accessible and beneficial to all communities.
                    </p>
                  </div>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <div>
                    <p className='font-medium'>Collaborative Innovation</p>
                    <p className='text-sm text-muted-foreground'>
                      We foster collaboration between volunteers, clients, and stakeholders.
                    </p>
                  </div>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <div>
                    <p className='font-medium'>Sustainable Impact</p>
                    <p className='text-sm text-muted-foreground'>
                      We focus on creating long-term, sustainable solutions for our partners.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Volunteer Expectations */}
          <Card>
            <CardHeader>
              <CardTitle>What We Expect from Volunteers</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-4'>
                  <h4 className='font-medium flex items-center gap-2'>
                    <Clock className='h-4 w-4' />
                    Time Commitment
                  </h4>
                  <ul className='space-y-2 text-sm text-muted-foreground'>
                    <li>• Minimum 5 hours per week commitment</li>
                    <li>• Consistent availability for project duration</li>
                    <li>• Advance notice for schedule changes</li>
                    <li>• Participation in team meetings when required</li>
                  </ul>
                </div>
                
                <div className='space-y-4'>
                  <h4 className='font-medium flex items-center gap-2'>
                    <CheckCircle className='h-4 w-4' />
                    Professional Standards
                  </h4>
                  <ul className='space-y-2 text-sm text-muted-foreground'>
                    <li>• Maintain professional communication</li>
                    <li>• Meet agreed-upon deadlines</li>
                    <li>• Follow project guidelines and standards</li>
                    <li>• Respect confidentiality agreements</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Types */}
          <Card>
            <CardHeader>
              <CardTitle>Types of Projects</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='p-4 border rounded-lg'>
                  <h4 className='font-medium mb-2'>Web Development</h4>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Build responsive websites and web applications for nonprofits and community organizations.
                  </p>
                  <div className='flex flex-wrap gap-1'>
                    <Badge variant='outline' className='text-xs'>React</Badge>
                    <Badge variant='outline' className='text-xs'>Next.js</Badge>
                    <Badge variant='outline' className='text-xs'>TypeScript</Badge>
                  </div>
                </div>
                
                <div className='p-4 border rounded-lg'>
                  <h4 className='font-medium mb-2'>Mobile Applications</h4>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Develop mobile apps that help organizations connect with their communities.
                  </p>
                  <div className='flex flex-wrap gap-1'>
                    <Badge variant='outline' className='text-xs'>React Native</Badge>
                    <Badge variant='outline' className='text-xs'>Flutter</Badge>
                    <Badge variant='outline' className='text-xs'>Swift</Badge>
                  </div>
                </div>
                
                <div className='p-4 border rounded-lg'>
                  <h4 className='font-medium mb-2'>UI/UX Design</h4>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Create user-friendly designs that improve accessibility and user experience.
                  </p>
                  <div className='flex flex-wrap gap-1'>
                    <Badge variant='outline' className='text-xs'>Figma</Badge>
                    <Badge variant='outline' className='text-xs'>Adobe XD</Badge>
                    <Badge variant='outline' className='text-xs'>Sketch</Badge>
                  </div>
                </div>
                
                <div className='p-4 border rounded-lg'>
                  <h4 className='font-medium mb-2'>Data Analysis</h4>
                  <p className='text-sm text-muted-foreground mb-3'>
                    Help organizations understand their data and make informed decisions.
                  </p>
                  <div className='flex flex-wrap gap-1'>
                    <Badge variant='outline' className='text-xs'>Python</Badge>
                    <Badge variant='outline' className='text-xs'>R</Badge>
                    <Badge variant='outline' className='text-xs'>SQL</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Getting Started */}
          <Card>
            <CardHeader>
              <CardTitle>Getting Started</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-4'>
                <div className='flex items-start gap-4'>
                  <div className='w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium'>
                    1
                  </div>
                  <div>
                    <p className='font-medium'>Complete Your Application</p>
                    <p className='text-sm text-muted-foreground'>
                      Fill out the volunteer application with your skills, availability, and project preferences.
                    </p>
                  </div>
                </div>
                
                <div className='flex items-start gap-4'>
                  <div className='w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium'>
                    2
                  </div>
                  <div>
                    <p className='font-medium'>Sign the NDA Agreement</p>
                    <p className='text-sm text-muted-foreground'>
                      Review and sign the non-disclosure agreement to access confidential project information.
                    </p>
                  </div>
                </div>
                
                <div className='flex items-start gap-4'>
                  <div className='w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium'>
                    3
                  </div>
                  <div>
                    <p className='font-medium'>Wait for Approval</p>
                    <p className='text-sm text-muted-foreground'>
                      Our team will review your application and match you with suitable projects.
                    </p>
                  </div>
                </div>
                
                <div className='flex items-start gap-4'>
                  <div className='w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium'>
                    4
                  </div>
                  <div>
                    <p className='font-medium'>Start Contributing</p>
                    <p className='text-sm text-muted-foreground'>
                      Join your assigned project team and start making a difference!
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground mb-4'>
                If you have any questions about the volunteer program or need assistance, 
                don't hesitate to reach out to our team.
              </p>
              
              <div className='space-y-2'>
                <p className='text-sm'>
                  <strong>Email:</strong> <EMAIL>
                </p>
                <p className='text-sm'>
                  <strong>Response Time:</strong> Within 24-48 hours
                </p>
              </div>
            </CardContent>
          </Card>
        </section>
      </ScrollArea>
    </main>
  );
}
