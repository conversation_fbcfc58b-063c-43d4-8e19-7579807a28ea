'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Calendar, Edit, MessageSquare, Paperclip, Save, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { AssigneeUser } from '@/components/issues/assignee-user';
import { PrioritySelector } from '@/components/issues/priority-selector';
import { StatusSelector } from '@/components/issues/status-selector';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useIssues, useMembers } from '@/hooks/use-db';
import type { Issue, UpdateIssueInput } from '@/lib/supabase/database-modules';

interface IssueDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface PriorityData {
  id: string;
  name: string;
  icon_name?: string | null;
  sort_order?: number | null;
}

interface StatusData {
  id: string;
  name: string;
  color: string;
  sort_order: number;
}

interface UserData {
  id: string;
  name: string;
  email: string;
  avatar_url?: string | null;
  status?: 'online' | 'offline' | 'away';
}

// Helper functions to convert issue data to component format
const convertPriority = (priority: any): PriorityData | null => {
  if (!priority) return null;
  return {
    id: priority.id,
    name: priority.name,
    icon_name: priority.icon_name,
    sort_order: priority.sort_order,
  };
};

const convertStatus = (status: any): StatusData | null => {
  if (!status) return null;
  return {
    id: status.id,
    name: status.name,
    color: status.color,
    sort_order: status.sort_order,
  };
};

const convertUser = (user: any): UserData | null => {
  if (!user) return null;
  return {
    id: user.id,
    name: user.full_name || user.name || 'Unknown User',
    email: user.email || '',
    avatar_url: user.avatar_url,
    status: 'online', // Default status
  };
};

export default function IssueDetailPage({ params }: IssueDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { getIssueById, updateIssue } = useIssues();
  const { members } = useMembers();

  const [issue, setIssue] = useState<Issue | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<UpdateIssueInput>({});
  const [isUpdating, setIsUpdating] = useState(false);

  const issueId = resolvedParams.id;

  // Fetch issue data
  useEffect(() => {
    if (issueId) {
      setLoading(true);
      getIssueById(issueId)
        .then((fetchedIssue) => {
          if (fetchedIssue) {
            setIssue(fetchedIssue);
            setEditForm({
              title: fetchedIssue.title,
              description: fetchedIssue.description,
              due_date: fetchedIssue.due_date,
            });
          } else {
            setNotFound(true);
          }
        })
        .catch((error) => {
          console.error('Error fetching issue:', error);
          toast.error('Failed to load issue');
          setNotFound(true);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [issueId, getIssueById]);

  const handleSaveEdit = () => {
    if (!issue || !editForm.title?.trim()) {
      toast.error('Title is required');
      return;
    }

    setIsUpdating(true);
    updateIssue(issue.id, editForm)
      .then(() => {
        toast.success('Issue updated successfully');
        setIsEditing(false);
        // Refresh issue data
        return getIssueById(issue.id);
      })
      .then((updatedIssue) => {
        if (updatedIssue) {
          setIssue(updatedIssue);
        }
      })
      .catch((error) => {
        console.error('Error updating issue:', error);
        toast.error('Failed to update issue');
      })
      .finally(() => {
        setIsUpdating(false);
      });
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditForm({
      title: issue?.title,
      description: issue?.description,
      due_date: issue?.due_date,
    });
  };

  if (loading) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Loading Issue...</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-muted-foreground'>Loading issue details...</div>
          </div>
        </section>
      </main>
    );
  }

  if (notFound || !issue) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Issue Not Found</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-center space-y-4'>
              <div className='text-destructive'>Issue not found</div>
              <Button onClick={() => router.push('/admin/issues')}>
                Go to Issues
              </Button>
            </div>
          </div>
        </section>
      </main>
    );
  }

  const creator = members.find(m => m.id === issue.created_by);
  const displayName = issue.title || 'Untitled Issue';

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.back()}
            className='h-8 w-8 p-0'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <p className='font-medium text-sm'>{issue.identifier}</p>
        </div>
        <div className='flex items-center gap-2'>
          {isEditing ? (
            <>
              <Button
                variant='ghost'
                size='sm'
                onClick={handleCancelEdit}
                disabled={isUpdating}
              >
                <X className='h-4 w-4' />
                Cancel
              </Button>
              <Button
                size='sm'
                onClick={handleSaveEdit}
                disabled={isUpdating}
              >
                <Save className='h-4 w-4' />
                {isUpdating ? 'Saving...' : 'Save'}
              </Button>
            </>
          ) : (
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setIsEditing(true)}
            >
              <Edit className='h-4 w-4' />
              Edit
            </Button>
          )}
        </div>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Main Content */}
          <div className='lg:col-span-2 space-y-6'>
            <Card>
              <CardHeader>
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    {isEditing ? (
                      <Input
                        value={editForm.title || ''}
                        onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                        className='text-lg font-semibold'
                        placeholder='Issue title'
                      />
                    ) : (
                      <CardTitle className='text-lg'>{displayName}</CardTitle>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div>
                  <Label className='text-sm font-medium'>Description</Label>
                  {isEditing ? (
                    <Textarea
                      value={editForm.description || ''}
                      onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                      className='mt-2 min-h-[120px]'
                      placeholder='Issue description'
                    />
                  ) : (
                    <div className='mt-2 text-sm text-muted-foreground whitespace-pre-wrap'>
                      {issue.description || 'No description provided'}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='text-base'>Issue Details</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div>
                  <Label className='text-sm font-medium'>Status</Label>
                  <div className='mt-2'>
                    <StatusSelector
                      status={convertStatus(issue.status)}
                      issueId={issue.id}
                    />
                  </div>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Priority</Label>
                  <div className='mt-2'>
                    <PrioritySelector
                      priority={convertPriority(issue.priority)}
                      issueId={issue.id}
                    />
                  </div>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Assignee</Label>
                  <div className='mt-2'>
                    <AssigneeUser
                      user={convertUser(issue.assignee)}
                      issueId={issue.id}
                    />
                  </div>
                </div>

                {issue.project && (
                  <div>
                    <Label className='text-sm font-medium'>Project</Label>
                    <div className='mt-2'>
                      <Badge variant='outline'>{issue.project.name}</Badge>
                    </div>
                  </div>
                )}

                <div>
                  <Label className='text-sm font-medium'>Due Date</Label>
                  {isEditing ? (
                    <Input
                      type='date'
                      value={editForm.due_date ? new Date(editForm.due_date).toISOString().split('T')[0] : ''}
                      onChange={(e) => setEditForm(prev => ({ ...prev, due_date: e.target.value ? new Date(e.target.value).toISOString() : null }))}
                      className='mt-2'
                    />
                  ) : (
                    <div className='mt-2 text-sm text-muted-foreground'>
                      {issue.due_date ? (
                        <div className='flex items-center gap-2'>
                          <Calendar className='h-4 w-4' />
                          {new Date(issue.due_date).toLocaleDateString()}
                        </div>
                      ) : (
                        'No due date set'
                      )}
                    </div>
                  )}
                </div>

                <Separator />

                <div>
                  <Label className='text-sm font-medium'>Created</Label>
                  <div className='mt-2 text-sm text-muted-foreground'>
                    {new Date(issue.created_at).toLocaleDateString()} by{' '}
                    {creator?.full_name || 'Unknown'}
                  </div>
                </div>

                <div>
                  <Label className='text-sm font-medium'>Updated</Label>
                  <div className='mt-2 text-sm text-muted-foreground'>
                    {new Date(issue.updated_at).toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  );
}
