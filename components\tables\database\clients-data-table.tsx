'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { DataTable } from '@/components/ui/data-table';
import { useClients } from '@/hooks/use-db';
import type { Client } from '@/lib/supabase/database-modules';

import { createColumns } from './clients-columns';

export function ClientsDataTable() {
  const { clients, loading, error, updateClient, deleteClient } = useClients();
  const router = useRouter();
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  const handleViewClient = (client: Client) => {
    router.push(`/admin/clients/${client.id}`);
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    // TODO: Open edit dialog/modal
  };

  const handleDeleteClient = (client: Client) => {
    const clientName = client.name || 'Unknown Client';
    if (confirm(`Are you sure you want to delete ${clientName}?`)) {
      const deletePromise = deleteClient(client.id);

      deletePromise
        .then(() => {
          toast.success(`${clientName} has been deleted successfully`);
        })
        .catch((error) => {
          toast.error(
            `Failed to delete client: ${error?.message || 'Unknown error'}`
          );
        });
    }
  };

  const handleStatusChange = (clientId: string, isActive: boolean) => {
    const updatePromise = updateClient(clientId, { is_active: isActive });

    updatePromise
      .then(() => {
        toast.success(
          `Client ${isActive ? 'activated' : 'deactivated'} successfully`
        );
      })
      .catch((error) => {
        toast.error(
          `Failed to update client status: ${error?.message || 'Unknown error'}`
        );
      });
  };

  const columns = createColumns({
    onViewClient: handleViewClient,
    onEditClient: handleEditClient,
    onDeleteClient: handleDeleteClient,
    onStatusChange: handleStatusChange,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-lg font-semibold'>Clients Database</h2>
        <p className='text-sm text-muted-foreground'>
          Manage all client records with full CRUD operations
        </p>
      </div>
      <DataTable columns={columns} data={clients} isLoading={loading} />
    </div>
  );
}
