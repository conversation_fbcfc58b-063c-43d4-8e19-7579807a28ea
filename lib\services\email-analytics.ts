// Email delivery tracking, error monitoring, and usage analytics
// Following utility object pattern instead of class

interface EmailMetric {
  id: string;
  emailId: string;
  type: string;
  event: 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'failed';
  timestamp: string;
  recipient?: string;
  metadata?: Record<string, unknown>;
}

interface EmailAnalytics {
  totalSent: number;
  totalDelivered: number;
  totalOpened: number;
  totalClicked: number;
  totalBounced: number;
  totalComplaints: number;
  totalFailed: number;
  deliveryRate: number;
  openRate: number;
  clickRate: number;
  bounceRate: number;
  complaintRate: number;
  failureRate: number;
}

interface EmailTypeStats {
  type: string;
  count: number;
  analytics: EmailAnalytics;
  recentEmails: EmailMetric[];
}

interface PerformanceMetrics {
  averageProcessingTime: number;
  queueSize: number;
  processingRate: number;
  errorRate: number;
  retryRate: number;
  throughput: number; // emails per hour
}

// In-memory storage (in production, use database)
const emailMetrics: Map<string, EmailMetric> = new Map();
const performanceData: Array<{
  timestamp: string;
  processingTime: number;
  queueSize: number;
  errors: number;
  retries: number;
}> = [];

const emailAnalytics = {
  // Track email event
  trackEmailEvent: (
    emailId: string,
    type: string,
    event: EmailMetric['event'],
    recipient?: string,
    metadata?: Record<string, unknown>
  ): Promise<EmailMetric> => {
    return new Promise((resolve) => {
      const metricId = `${emailId}_${event}_${Date.now()}`;
      const metric: EmailMetric = {
        id: metricId,
        emailId,
        type,
        event,
        timestamp: new Date().toISOString(),
        recipient,
        metadata
      };

      emailMetrics.set(metricId, metric);
      
      console.log(`Email event tracked: ${event} for ${emailId}`, {
        type,
        recipient,
        timestamp: metric.timestamp
      });

      resolve(metric);
    });
  },

  // Get analytics for a specific email type
  getEmailTypeAnalytics: (emailType: string, timeRange?: { start: string; end: string }): Promise<EmailTypeStats> => {
    return new Promise((resolve) => {
      const typeMetrics = Array.from(emailMetrics.values()).filter(metric => {
        if (metric.type !== emailType) return false;
        
        if (timeRange) {
          return metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end;
        }
        
        return true;
      });

      const analytics = emailAnalytics.calculateAnalytics(typeMetrics);
      const recentEmails = typeMetrics
        .sort((a, b) => b.timestamp.localeCompare(a.timestamp))
        .slice(0, 10);

      resolve({
        type: emailType,
        count: typeMetrics.length,
        analytics,
        recentEmails
      });
    });
  },

  // Calculate analytics from metrics
  calculateAnalytics: (metrics: EmailMetric[]): EmailAnalytics => {
    const eventCounts = {
      sent: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      bounced: 0,
      complained: 0,
      failed: 0
    };

    metrics.forEach(metric => {
      eventCounts[metric.event]++;
    });

    const totalSent = eventCounts.sent;
    const safeDiv = (a: number, b: number) => b === 0 ? 0 : (a / b) * 100;

    return {
      totalSent: eventCounts.sent,
      totalDelivered: eventCounts.delivered,
      totalOpened: eventCounts.opened,
      totalClicked: eventCounts.clicked,
      totalBounced: eventCounts.bounced,
      totalComplaints: eventCounts.complained,
      totalFailed: eventCounts.failed,
      deliveryRate: safeDiv(eventCounts.delivered, totalSent),
      openRate: safeDiv(eventCounts.opened, eventCounts.delivered),
      clickRate: safeDiv(eventCounts.clicked, eventCounts.opened),
      bounceRate: safeDiv(eventCounts.bounced, totalSent),
      complaintRate: safeDiv(eventCounts.complained, eventCounts.delivered),
      failureRate: safeDiv(eventCounts.failed, totalSent)
    };
  },

  // Get overall analytics across all email types
  getOverallAnalytics: (timeRange?: { start: string; end: string }): Promise<EmailAnalytics> => {
    return new Promise((resolve) => {
      let allMetrics = Array.from(emailMetrics.values());
      
      if (timeRange) {
        allMetrics = allMetrics.filter(metric => 
          metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
        );
      }

      const analytics = emailAnalytics.calculateAnalytics(allMetrics);
      resolve(analytics);
    });
  },

  // Track performance metrics
  trackPerformanceMetric: (
    processingTime: number,
    queueSize: number,
    errors: number = 0,
    retries: number = 0
  ): Promise<void> => {
    return new Promise((resolve) => {
      const performanceMetric = {
        timestamp: new Date().toISOString(),
        processingTime,
        queueSize,
        errors,
        retries
      };

      performanceData.push(performanceMetric);

      // Keep only last 1000 entries to prevent memory issues
      if (performanceData.length > 1000) {
        performanceData.shift();
      }

      resolve();
    });
  },

  // Get performance metrics
  getPerformanceMetrics: (timeRange?: { start: string; end: string }): Promise<PerformanceMetrics> => {
    return new Promise((resolve) => {
      let data = performanceData;
      
      if (timeRange) {
        data = performanceData.filter(metric => 
          metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
        );
      }

      if (data.length === 0) {
        resolve({
          averageProcessingTime: 0,
          queueSize: 0,
          processingRate: 0,
          errorRate: 0,
          retryRate: 0,
          throughput: 0
        });
        return;
      }

      const totalProcessingTime = data.reduce((sum, d) => sum + d.processingTime, 0);
      const totalErrors = data.reduce((sum, d) => sum + d.errors, 0);
      const totalRetries = data.reduce((sum, d) => sum + d.retries, 0);
      const averageQueueSize = data.reduce((sum, d) => sum + d.queueSize, 0) / data.length;

      // Calculate throughput (emails per hour)
      const timeSpanHours = timeRange ? 
        (new Date(timeRange.end).getTime() - new Date(timeRange.start).getTime()) / (1000 * 60 * 60) :
        1; // Default to 1 hour if no range specified

      const emailsSent = Array.from(emailMetrics.values()).filter(metric => {
        if (metric.event !== 'sent') return false;
        if (timeRange) {
          return metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end;
        }
        return true;
      }).length;

      resolve({
        averageProcessingTime: totalProcessingTime / data.length,
        queueSize: averageQueueSize,
        processingRate: data.length / timeSpanHours,
        errorRate: (totalErrors / data.length) * 100,
        retryRate: (totalRetries / data.length) * 100,
        throughput: emailsSent / timeSpanHours
      });
    });
  },

  // Get email types with their stats
  getEmailTypesSummary: (timeRange?: { start: string; end: string }): Promise<EmailTypeStats[]> => {
    return new Promise((resolve) => {
      let allMetrics = Array.from(emailMetrics.values());
      
      if (timeRange) {
        allMetrics = allMetrics.filter(metric => 
          metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
        );
      }

      const typeGroups = new Map<string, EmailMetric[]>();
      
      allMetrics.forEach(metric => {
        if (!typeGroups.has(metric.type)) {
          typeGroups.set(metric.type, []);
        }
        typeGroups.get(metric.type)!.push(metric);
      });

      const typeStats: EmailTypeStats[] = [];
      
      for (const [type, metrics] of typeGroups.entries()) {
        const analytics = emailAnalytics.calculateAnalytics(metrics);
        const recentEmails = metrics
          .sort((a, b) => b.timestamp.localeCompare(a.timestamp))
          .slice(0, 5);

        typeStats.push({
          type,
          count: metrics.length,
          analytics,
          recentEmails
        });
      }

      // Sort by count (most active first)
      typeStats.sort((a, b) => b.count - a.count);

      resolve(typeStats);
    });
  },

  // Get health check data
  getHealthCheck: (): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    metrics: {
      recentFailures: number;
      queueBacklog: number;
      averageResponseTime: number;
      errorRate: number;
    };
    issues: string[];
  }> => {
    return new Promise((resolve) => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000).toISOString();
      
      // Get recent metrics
      const recentMetrics = Array.from(emailMetrics.values()).filter(
        metric => metric.timestamp >= oneHourAgo
      );
      
      const recentPerformance = performanceData.filter(
        data => data.timestamp >= oneHourAgo
      );

      const recentFailures = recentMetrics.filter(m => m.event === 'failed').length;
      const queueBacklog = recentPerformance.length > 0 ? 
        recentPerformance[recentPerformance.length - 1].queueSize : 0;
      
      const averageResponseTime = recentPerformance.length > 0 ?
        recentPerformance.reduce((sum, d) => sum + d.processingTime, 0) / recentPerformance.length : 0;
      
      const totalRecentEmails = recentMetrics.filter(m => m.event === 'sent').length;
      const errorRate = totalRecentEmails > 0 ? (recentFailures / totalRecentEmails) * 100 : 0;

      const issues: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // Check for issues
      if (errorRate > 10) {
        issues.push(`High error rate: ${errorRate.toFixed(1)}%`);
        status = 'critical';
      } else if (errorRate > 5) {
        issues.push(`Elevated error rate: ${errorRate.toFixed(1)}%`);
        status = 'warning';
      }

      if (queueBacklog > 100) {
        issues.push(`Large queue backlog: ${queueBacklog} emails`);
        status = status === 'critical' ? 'critical' : 'warning';
      }

      if (averageResponseTime > 5000) {
        issues.push(`Slow response time: ${averageResponseTime.toFixed(0)}ms`);
        status = status === 'critical' ? 'critical' : 'warning';
      }

      resolve({
        status,
        metrics: {
          recentFailures,
          queueBacklog,
          averageResponseTime,
          errorRate
        },
        issues
      });
    });
  },

  // Clean up old metrics
  cleanupMetrics: (maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<number> => {
    return new Promise((resolve) => {
      const cutoffTime = new Date(Date.now() - maxAge).toISOString();
      let cleanedCount = 0;

      for (const [id, metric] of emailMetrics.entries()) {
        if (metric.timestamp < cutoffTime) {
          emailMetrics.delete(id);
          cleanedCount++;
        }
      }

      // Clean performance data
      const originalLength = performanceData.length;
      performanceData.splice(0, performanceData.findIndex(d => d.timestamp >= cutoffTime));
      cleanedCount += originalLength - performanceData.length;

      console.log(`Cleaned up ${cleanedCount} old analytics records`);
      resolve(cleanedCount);
    });
  }
};

export { emailAnalytics };
export type { EmailMetric, EmailAnalytics, EmailTypeStats, PerformanceMetrics };
