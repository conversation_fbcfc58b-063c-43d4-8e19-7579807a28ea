'use client';

import { Download, File, Paperclip, Plus, Trash2, Upload } from 'lucide-react';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIssues, useProfile } from '@/hooks/use-db';

interface Attachment {
  id: string;
  file_name: string;
  file_url: string;
  file_size: number;
  file_type: string;
  created_at: string;
  uploaded_by: string;
  uploader?: {
    id: string;
    full_name: string;
    email: string;
  };
}

interface IssueAttachmentsProps {
  issueId: string;
  attachments?: Attachment[];
  onAttachmentAdded?: () => void;
  canUpload?: boolean;
}

export function IssueAttachments({ 
  issueId, 
  attachments = [], 
  onAttachmentAdded,
  canUpload = true 
}: IssueAttachmentsProps) {
  const { uploadIssueAttachment } = useIssues();
  const { profile } = useProfile();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !profile?.id) return;

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setIsUploading(true);
    uploadIssueAttachment(issueId, file, profile.id)
      .then((fileUrl) => {
        toast.success('File uploaded successfully');
        onAttachmentAdded?.();
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      })
      .catch((error) => {
        console.error('Error uploading file:', error);
        toast.error('Failed to upload file');
      })
      .finally(() => {
        setIsUploading(false);
      });
  };

  const handleDownload = (attachment: Attachment) => {
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = attachment.file_url;
    link.download = attachment.file_name;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('Download started');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return '🖼️';
    } else if (fileType.includes('pdf')) {
      return '📄';
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return '📝';
    } else if (fileType.includes('sheet') || fileType.includes('excel')) {
      return '📊';
    } else if (fileType.includes('zip') || fileType.includes('rar')) {
      return '🗜️';
    } else {
      return '📎';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center gap-2'>
            <Paperclip className='h-5 w-5' />
            Attachments ({attachments.length})
          </CardTitle>
          {canUpload && (
            <Button 
              variant='outline' 
              size='sm' 
              onClick={handleFileSelect}
              disabled={isUploading}
            >
              <Plus className='h-4 w-4 mr-2' />
              {isUploading ? 'Uploading...' : 'Add File'}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type='file'
          onChange={handleFileChange}
          className='hidden'
          accept='*/*'
        />

        {/* Upload Progress */}
        {isUploading && (
          <div className='flex items-center gap-2 p-3 border rounded-lg mb-4 bg-muted/50'>
            <Upload className='h-4 w-4 animate-pulse' />
            <span className='text-sm'>Uploading file...</span>
          </div>
        )}

        {/* Attachments List */}
        {attachments.length > 0 ? (
          <ScrollArea className='max-h-[300px]'>
            <div className='space-y-2'>
              {attachments.map((attachment) => (
                <div 
                  key={attachment.id} 
                  className='flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors'
                >
                  <div className='flex items-center gap-3 flex-1 min-w-0'>
                    <div className='text-2xl flex-shrink-0'>
                      {getFileIcon(attachment.file_type)}
                    </div>
                    <div className='flex-1 min-w-0'>
                      <p className='text-sm font-medium truncate'>
                        {attachment.file_name}
                      </p>
                      <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                        <span>{formatFileSize(attachment.file_size)}</span>
                        <span>•</span>
                        <span>
                          {new Date(attachment.created_at).toLocaleDateString()}
                        </span>
                        {attachment.uploader && (
                          <>
                            <span>•</span>
                            <span>{attachment.uploader.full_name}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => handleDownload(attachment)}
                      className='h-8 w-8 p-0'
                    >
                      <Download className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className='text-center py-8 text-muted-foreground'>
            <Paperclip className='h-12 w-12 mx-auto mb-3 opacity-50' />
            <p className='text-sm'>No attachments yet</p>
            {canUpload && (
              <p className='text-xs'>Click "Add File" to upload attachments</p>
            )}
          </div>
        )}

        {/* Upload Guidelines */}
        {canUpload && (
          <div className='mt-4 p-3 bg-muted/30 rounded-lg'>
            <p className='text-xs text-muted-foreground'>
              <strong>Upload Guidelines:</strong> Maximum file size is 10MB. 
              Supported formats include images, documents, PDFs, and archives.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
