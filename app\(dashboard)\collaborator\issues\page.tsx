'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { CollaboratorIssuesMetrics } from '@/components/issues/collaborator-issues-metrics';
import { CollaboratorIssuesTable } from '@/components/tables/collaborator-issues/collaborator-issues-table';
import { useIssues, usePortfolio } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';

export default function CollaboratorIssuesPage() {
  const { getCollaboratorIssues } = useIssues();
  const { portfolioData } = usePortfolio();
  const [issues, setIssues] = useState<Issue[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!portfolioData?.id) return;

    setLoading(true);

    getCollaboratorIssues(portfolioData.id)
      .then((collaboratorIssues) => {
        setIssues(collaboratorIssues);
      })
      .catch((error) => {
        console.error('Error fetching collaborator issues:', error);
        toast.error('Failed to load issues');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [portfolioData?.id, getCollaboratorIssues]);

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>My Issues</p>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <CollaboratorIssuesMetrics issues={issues} loading={loading} />
        <CollaboratorIssuesTable issues={issues} loading={loading} />
      </section>
    </main>
  );
}
