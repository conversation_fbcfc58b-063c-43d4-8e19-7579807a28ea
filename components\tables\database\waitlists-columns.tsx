'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import {
  CheckCircle,
  Clock,
  Edit,
  Eye,
  MoreHorizontal,
  Trash2,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import type { Waitlist } from '@/lib/supabase/database-modules';

interface ColumnsProps {
  onViewWaitlist: (waitlist: Waitlist) => void;
  onEditWaitlist: (waitlist: Waitlist) => void;
  onDeleteWaitlist: (waitlist: Waitlist) => void;
  onEmailStatusChange: (waitlistId: number, isEmailSent: boolean) => void;
}

export const createColumns = ({
  onViewWaitlist,
  onEditWaitlist,
  onDeleteWaitlist,
  onEmailStatusChange,
}: ColumnsProps): ColumnDef<Waitlist>[] => [
  {
    accessorKey: 'full_name',
    header: 'Name',
    cell: ({ row }) => {
      const waitlist = row.original;
      return (
        <div className='flex flex-col'>
          <span className='font-medium'>{waitlist.full_name || 'Unknown'}</span>
          <span className='text-xs text-muted-foreground'>
            {waitlist.email_address}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'is_ld_email_sent',
    header: 'Email Status',
    cell: ({ row }) => {
      const waitlist = row.original;
      const isEmailSent = waitlist.is_ld_email_sent;
      return (
        <Badge
          variant={isEmailSent ? 'default' : 'secondary'}
          className='text-xs'
        >
          {isEmailSent ? 'Sent' : 'Pending'}
        </Badge>
      );
    },
  },

  {
    accessorKey: 'email_action',
    header: 'Email Action',
    cell: ({ row }) => {
      const waitlist = row.original;
      const isEmailSent = waitlist.is_ld_email_sent;

      return (
        <Select
          value={isEmailSent ? 'sent' : 'pending'}
          onValueChange={(newStatus) =>
            onEmailStatusChange(waitlist.id, newStatus === 'sent')
          }
        >
          <SelectTrigger className='w-32 h-8'>
            <SelectValue>
              <Badge
                variant={isEmailSent ? 'default' : 'secondary'}
                className='text-xs'
              >
                {isEmailSent ? 'Sent' : 'Pending'}
              </Badge>
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='pending'>
              <div className='flex items-center'>
                <Clock className='w-3 h-3 mr-2' />
                Pending
              </div>
            </SelectItem>
            <SelectItem value='sent'>
              <div className='flex items-center'>
                <CheckCircle className='w-3 h-3 mr-2' />
                Sent
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Joined',
    cell: ({ row }) => {
      const createdAt = row.getValue('created_at') as string;

      if (!createdAt) {
        return <span className='text-muted-foreground text-sm'>Unknown</span>;
      }

      const date = new Date(createdAt);
      if (Number.isNaN(date.getTime())) {
        return (
          <span className='text-muted-foreground text-sm'>Invalid Date</span>
        );
      }

      return (
        <div className='text-sm'>
          <div>{format(date, 'MMM dd, yyyy')}</div>
          <div className='text-xs text-muted-foreground'>
            {format(date, 'HH:mm')}
          </div>
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const waitlist = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onViewWaitlist(waitlist)}>
              <Eye className='mr-2 h-4 w-4' />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEditWaitlist(waitlist)}>
              <Edit className='mr-2 h-4 w-4' />
              Edit Entry
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDeleteWaitlist(waitlist)}
              className='text-destructive'
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Delete Entry
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
