'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Eye, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

import { DatePicker } from '@/components/issues/date-picker';
import { LabelBadge } from '@/components/issues/label-badge';
import { PrioritySelector } from '@/components/issues/priority-selector';

import { StatusSelector } from '@/components/issues/status-selector';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useIssues } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';

interface ColumnsProps {
  onViewIssue?: (issue: Issue) => void;
}

export const createColumns = ({
  onViewIssue,
}: ColumnsProps = {}): ColumnDef<Issue>[] => {
  return [
    {
      accessorKey: 'title',
      header: 'Title',
      cell: ({ row }) => {
        const issue = row.original;
        return (
          <div className='flex items-center gap-2'>
            <div className='flex flex-col items-start overflow-hidden'>
              <Link
                href={`/admin/issues/${issue.id}`}
                className='font-medium truncate w-full hover:underline'
              >
                {issue.title}
              </Link>
              {issue.description && (
                <p className='text-xs text-muted-foreground truncate w-full'>
                  {issue.description}
                </p>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const issue = row.original;
        const { updateIssue } = useIssues();

        const handleStatusChange = (statusId: string) => {
          const updatePromise = updateIssue(issue.id, { status_id: statusId });

          toast.promise(updatePromise, {
            loading: 'Updating issue status...',
            success: 'Issue status updated successfully',
            error: (error) =>
              `Failed to update status: ${error?.message || 'Unknown error'}`,
          });
        };

        return (
          <StatusSelector
            status={issue.status || null}
            issueId={issue.id}
            onChange={(status) => {
              if (status) {
                handleStatusChange(status.id);
              }
            }}
          />
        );
      },
    },
    {
      accessorKey: 'priority',
      header: 'Priority',
      cell: ({ row }) => {
        const issue = row.original;
        const { updateIssue } = useIssues();

        const handlePriorityChange = (priorityId: string) => {
          const updatePromise = updateIssue(issue.id, {
            priority_id: priorityId,
          });

          toast.promise(updatePromise, {
            loading: 'Updating issue priority...',
            success: 'Issue priority updated successfully',
            error: (error) =>
              `Failed to update priority: ${error?.message || 'Unknown error'}`,
          });
        };

        return (
          <PrioritySelector
            priority={issue.priority || null}
            issueId={issue.id}
            onChange={(priority) => {
              if (priority) {
                handlePriorityChange(priority.id);
              }
            }}
          />
        );
      },
    },
    {
      accessorKey: 'labels',
      header: 'Labels',
      cell: ({ row }) => {
        const issue = row.original;
        if (!issue.labels || issue.labels.length === 0) {
          return (
            <span className='text-muted-foreground text-sm'>No labels</span>
          );
        }

        return (
          <div className='flex flex-wrap gap-1'>
            <LabelBadge labels={issue.labels.slice(0, 2)} />
            {issue.labels.length > 2 && (
              <span className='text-xs text-muted-foreground'>
                +{issue.labels.length - 2} more
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'due_date',
      header: 'Due Date',
      cell: ({ row }) => {
        const issue = row.original;
        const { updateIssue } = useIssues();

        const handleDateChange = (date: Date | null) => {
          const dateString = date ? date.toISOString() : null;
          const updatePromise = updateIssue(issue.id, { due_date: dateString });

          toast.promise(updatePromise, {
            loading: 'Updating due date...',
            success: 'Due date updated successfully',
            error: (error) =>
              `Failed to update due date: ${error?.message || 'Unknown error'}`,
          });
        };

        return (
          <DatePicker
            date={issue.due_date ? new Date(issue.due_date) : null}
            onDateChange={handleDateChange}
          />
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const issue = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' className='h-8 w-8 p-0'>
                <span className='sr-only'>Open menu</span>
                <MoreHorizontal className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end' className='w-64'>
              <DropdownMenuItem asChild>
                <Link href={`/admin/issues/${issue.id}`}>
                  <Eye className='mr-2 h-4 w-4' />
                  View Issue
                </Link>
              </DropdownMenuItem>

              <DropdownMenuSeparator />

              {/* Project Assignee Information */}
              <DropdownMenuLabel className='text-xs font-medium text-muted-foreground'>
                Project Assignee
              </DropdownMenuLabel>
              {issue.assignee ? (
                <div className='px-2 py-1.5 flex items-center gap-2'>
                  <Avatar className='size-6'>
                    <AvatarImage
                      src={issue.assignee.avatar_url || undefined}
                      alt={issue.assignee.full_name || issue.assignee.email}
                    />
                    <AvatarFallback className='text-xs'>
                      {issue.assignee.full_name?.charAt(0) ||
                        issue.assignee.email?.charAt(0) ||
                        'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className='flex flex-col'>
                    <span className='text-sm font-medium'>
                      {issue.assignee.full_name || issue.assignee.email}
                    </span>
                    {issue.assignee.full_name && (
                      <span className='text-xs text-muted-foreground'>
                        {issue.assignee.email}
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <div className='px-2 py-1.5 text-sm text-muted-foreground'>
                  Unassigned
                </div>
              )}

              <DropdownMenuSeparator />

              {/* Created By Information */}
              <DropdownMenuLabel className='text-xs font-medium text-muted-foreground'>
                Created By
              </DropdownMenuLabel>
              {issue.creator ? (
                <div className='px-2 py-1.5 flex items-center gap-2'>
                  <Avatar className='size-6'>
                    <AvatarImage
                      src={issue.creator.avatar_url || undefined}
                      alt={issue.creator.full_name || issue.creator.email}
                    />
                    <AvatarFallback className='text-xs'>
                      {issue.creator.full_name?.charAt(0) ||
                        issue.creator.email?.charAt(0) ||
                        'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className='flex flex-col'>
                    <span className='text-sm font-medium'>
                      {issue.creator.full_name || issue.creator.email}
                    </span>
                    {issue.creator.full_name && (
                      <span className='text-xs text-muted-foreground'>
                        {issue.creator.email}
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <div className='px-2 py-1.5 text-sm text-muted-foreground'>
                  Unknown creator
                </div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
};
