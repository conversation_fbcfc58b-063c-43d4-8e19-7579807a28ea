-- ============================================================================
-- Budget Management System Database Migration
-- ============================================================================
-- This script adds missing constraints, indexes, functions, and policies
-- for the budget management system
-- ============================================================================

-- ============================================================================
-- STEP 1: Create Enum Types for Better Data Integrity
-- ============================================================================

-- Create enum types if they don't exist
DO $$ BEGIN
    CREATE TYPE budget_status AS ENUM ('draft', 'approved', 'locked', 'spent');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE budget_category AS ENUM ('marketing', 'development', 'consulting', 'operations', 'other');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE currency_type AS ENUM ('USD', 'EUR', 'GBP', 'JPY');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payout_status AS ENUM ('pending', 'partially_paid', 'completed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ============================================================================
-- STEP 2: Add Missing Indexes for Performance
-- ============================================================================

-- Budget table indexes
CREATE INDEX IF NOT EXISTS idx_budgets_project_id ON budgets(ProjectId);
CREATE INDEX IF NOT EXISTS idx_budgets_client_id ON budgets(ClientId);
CREATE INDEX IF NOT EXISTS idx_budgets_affiliate_id ON budgets(affiliateId);
CREATE INDEX IF NOT EXISTS idx_budgets_status ON budgets(Status);
CREATE INDEX IF NOT EXISTS idx_budgets_category ON budgets(Category);
CREATE INDEX IF NOT EXISTS idx_budgets_start_date ON budgets(StartDate);
CREATE INDEX IF NOT EXISTS idx_budgets_end_date ON budgets(EndDate);
CREATE INDEX IF NOT EXISTS idx_budgets_approved_by ON budgets(ApprovedBy);
CREATE INDEX IF NOT EXISTS idx_budgets_has_affiliate ON budgets(has_affiliate);
CREATE INDEX IF NOT EXISTS idx_budgets_payout_status ON budgets(PayoutStatus);

-- Client table indexes
CREATE INDEX IF NOT EXISTS idx_clients_created_by ON clients(created_by);
CREATE INDEX IF NOT EXISTS idx_clients_is_active ON clients(is_active);
CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email);
CREATE INDEX IF NOT EXISTS idx_clients_company_name ON clients(company_name);
CREATE INDEX IF NOT EXISTS idx_clients_industry ON clients(industry);

-- Project budget integration indexes
CREATE INDEX IF NOT EXISTS idx_projects_budget_id ON projects(budget_id);
CREATE INDEX IF NOT EXISTS idx_projects_client_id ON projects(client_id);
CREATE INDEX IF NOT EXISTS idx_projects_affiliate_user ON projects(affiliate_user);
CREATE INDEX IF NOT EXISTS idx_projects_is_proposed ON projects(is_proposed);

-- Proposal table indexes
CREATE INDEX IF NOT EXISTS idx_proposals_user_id ON affiliate_proposals(user_id);
CREATE INDEX IF NOT EXISTS idx_proposals_client_id ON affiliate_proposals(client_id);
CREATE INDEX IF NOT EXISTS idx_proposals_is_approved ON affiliate_proposals(is_approved);
CREATE INDEX IF NOT EXISTS idx_proposals_completed ON affiliate_proposals(completed);
CREATE INDEX IF NOT EXISTS idx_proposals_created_at ON affiliate_proposals(created_at);

-- ============================================================================
-- STEP 3: Add Foreign Key Constraints
-- ============================================================================

-- Add foreign key constraints for budgets table
DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT fk_budgets_project 
    FOREIGN KEY (ProjectId) REFERENCES projects(id) ON DELETE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT fk_budgets_client 
    FOREIGN KEY (ClientId) REFERENCES clients(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT fk_budgets_affiliate 
    FOREIGN KEY (affiliateId) REFERENCES profiles(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT fk_budgets_approved_by 
    FOREIGN KEY (ApprovedBy) REFERENCES profiles(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add foreign key constraints for projects table
DO $$ BEGIN
    ALTER TABLE projects
    ADD CONSTRAINT fk_projects_budget 
    FOREIGN KEY (budget_id) REFERENCES budgets(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE projects
    ADD CONSTRAINT fk_projects_client 
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE projects
    ADD CONSTRAINT fk_projects_affiliate_user 
    FOREIGN KEY (affiliate_user) REFERENCES profiles(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add foreign key constraints for clients table
DO $$ BEGIN
    ALTER TABLE clients
    ADD CONSTRAINT fk_clients_created_by 
    FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add foreign key constraints for affiliate_proposals table
DO $$ BEGIN
    ALTER TABLE affiliate_proposals
    ADD CONSTRAINT fk_proposals_user 
    FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE affiliate_proposals
    ADD CONSTRAINT fk_proposals_client 
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ============================================================================
-- STEP 4: Add Check Constraints for Data Validation
-- ============================================================================

-- Budget validation constraints
DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT chk_budgets_amounts 
    CHECK (ActualAmount > 0 AND CurrentAmount >= 0);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT chk_budgets_current_not_exceed_actual 
    CHECK (CurrentAmount <= ActualAmount);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT chk_budgets_dates 
    CHECK (EndDate > StartDate);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    ALTER TABLE budgets 
    ADD CONSTRAINT chk_budgets_commission 
    CHECK (
        (has_affiliate = false AND AffiliateCommission IS NULL) OR 
        (has_affiliate = true AND AffiliateCommission > 0)
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Client validation constraints
DO $$ BEGIN
    ALTER TABLE clients
    ADD CONSTRAINT chk_clients_email 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email IS NULL);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ============================================================================
-- STEP 5: Create Database Functions and Triggers
-- ============================================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply timestamp triggers to all relevant tables
DROP TRIGGER IF EXISTS update_budgets_updated_at ON budgets;
CREATE TRIGGER update_budgets_updated_at 
    BEFORE UPDATE ON budgets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_clients_updated_at ON clients;
CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON clients 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_projects_updated_at ON projects;
CREATE TRIGGER update_projects_updated_at 
    BEFORE UPDATE ON projects 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to validate budget operations
CREATE OR REPLACE FUNCTION validate_budget_operation()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent modification of locked or spent budgets (except status changes)
    IF OLD.Status IN ('locked', 'spent') AND 
       (NEW.ActualAmount != OLD.ActualAmount OR NEW.CurrentAmount != OLD.CurrentAmount) THEN
        RAISE EXCEPTION 'Cannot modify amounts for budget with status: %', OLD.Status;
    END IF;
    
    -- Validate affiliate commission
    IF NEW.has_affiliate = true AND NEW.AffiliateCommission IS NULL THEN
        RAISE EXCEPTION 'Affiliate commission required when has_affiliate is true';
    END IF;
    
    -- Auto-set approval date when status changes to approved
    IF NEW.Status = 'approved' AND OLD.Status != 'approved' THEN
        NEW.ApprovalDate = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS validate_budget_before_update ON budgets;
CREATE TRIGGER validate_budget_before_update 
    BEFORE UPDATE ON budgets 
    FOR EACH ROW EXECUTE FUNCTION validate_budget_operation();

-- ============================================================================
-- STEP 6: Create Database Views for Better Data Access
-- ============================================================================

-- Budget summary view with related data
CREATE OR REPLACE VIEW budget_summary AS
SELECT 
    b.*,
    p.name as project_name,
    p.description as project_description,
    c.name as client_name,
    c.company_name as client_company,
    c.email as client_email,
    affiliate.full_name as affiliate_name,
    affiliate.email as affiliate_email,
    approver.full_name as approved_by_name,
    (b.ActualAmount - b.CurrentAmount) as spent_amount,
    CASE 
        WHEN b.ActualAmount > 0 THEN 
            ROUND(((b.ActualAmount - b.CurrentAmount) / b.ActualAmount * 100), 2)
        ELSE 0 
    END as spent_percentage,
    CASE 
        WHEN b.EndDate < CURRENT_DATE AND b.Status != 'completed' THEN true
        ELSE false 
    END as is_overdue
FROM budgets b
LEFT JOIN projects p ON b.ProjectId = p.id
LEFT JOIN clients c ON b.ClientId = c.id
LEFT JOIN profiles affiliate ON b.affiliateId = affiliate.id
LEFT JOIN profiles approver ON b.ApprovedBy = approver.id;

-- Client projects view
CREATE OR REPLACE VIEW client_projects AS
SELECT 
    c.*,
    COUNT(p.id) as project_count,
    SUM(b.ActualAmount) as total_budget,
    SUM(b.ActualAmount - b.CurrentAmount) as total_spent
FROM clients c
LEFT JOIN projects p ON c.id = p.client_id
LEFT JOIN budgets b ON p.budget_id = b.id
WHERE c.is_active = true
GROUP BY c.id, c.name, c.email, c.phone, c.description, c.company_name, 
         c.industry, c.contact_person, c.address, c.created_at, c.updated_at, 
         c.created_by, c.is_active;

-- ============================================================================
-- STEP 7: Verification Queries
-- ============================================================================

-- Verify the migration by checking constraints and indexes
SELECT 
    'Constraints' as type,
    conname as name,
    'budgets' as table_name
FROM pg_constraint 
WHERE conrelid = 'budgets'::regclass
UNION ALL
SELECT 
    'Indexes' as type,
    indexname as name,
    tablename as table_name
FROM pg_indexes 
WHERE tablename IN ('budgets', 'clients', 'projects', 'affiliate_proposals')
ORDER BY type, table_name, name;

-- Check if views were created successfully
SELECT 
    schemaname,
    viewname,
    definition IS NOT NULL as has_definition
FROM pg_views 
WHERE viewname IN ('budget_summary', 'client_projects');

COMMENT ON TABLE budgets IS 'Budget management for projects with affiliate commission tracking';
COMMENT ON TABLE clients IS 'Client information and contact details';
COMMENT ON VIEW budget_summary IS 'Comprehensive budget information with related project and client data';
COMMENT ON VIEW client_projects IS 'Client summary with project and budget aggregations';
