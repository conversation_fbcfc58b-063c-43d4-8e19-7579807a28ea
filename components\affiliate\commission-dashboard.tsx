'use client';

import {
  AlertCircle,
  Banknote,
  Calendar,
  Clock,
  DollarSign,
  Target,
} from 'lucide-react';
import { useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useBudgets, useProfile, useProposals } from '@/hooks/use-db';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/currency';

interface CommissionDashboardProps {
  className?: string;
}

export function CommissionDashboard({ className }: CommissionDashboardProps) {
  const { budgets } = useBudgets();
  const { proposals } = useProposals(true); // Get affiliate proposals
  const { profile } = useProfile();

  const commissionAnalytics = useMemo(() => {
    if (!profile?.id) return null;

    // Get affiliate budgets (budgets with affiliate commission)
    const affiliateBudgets = budgets.filter(
      (b) => b.has_affiliate && b.affiliateId === profile.id
    );

    // Calculate commission metrics
    const totalCommissionEarned = affiliateBudgets.reduce(
      (sum, budget) => sum + (budget.AffiliateCommission || 0),
      0
    );

    const pendingCommissions = affiliateBudgets
      .filter((b) => b.Status === 'approved' || b.Status === 'locked')
      .reduce((sum, budget) => sum + (budget.AffiliateCommission || 0), 0);

    const paidCommissions = affiliateBudgets
      .filter((b) => b.Status === 'spent')
      .reduce((sum, budget) => sum + (budget.AffiliateCommission || 0), 0);

    // Proposal metrics
    const approvedProposals = proposals.filter((p) => p.is_approved === true);
    const pendingProposals = proposals.filter((p) => p.is_approved === null);
    const rejectedProposals = proposals.filter((p) => p.is_approved === false);

    // Calculate conversion rate
    const totalProposals = proposals.length;
    const conversionRate =
      totalProposals > 0
        ? (approvedProposals.length / totalProposals) * 100
        : 0;

    // Monthly breakdown
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const thisMonthBudgets = affiliateBudgets.filter((budget) => {
      const budgetDate = new Date(budget.created_at);
      return (
        budgetDate.getMonth() === currentMonth &&
        budgetDate.getFullYear() === currentYear
      );
    });

    const thisMonthCommissions = thisMonthBudgets.reduce(
      (sum, budget) => sum + (budget.AffiliateCommission || 0),
      0
    );

    // Recent activity
    const recentBudgets = affiliateBudgets
      .sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
      .slice(0, 5);

    // Commission by status
    const commissionByStatus = {
      pending: pendingCommissions,
      paid: paidCommissions,
      total: totalCommissionEarned,
    };

    return {
      totalCommissionEarned,
      pendingCommissions,
      paidCommissions,
      thisMonthCommissions,
      conversionRate,
      proposalMetrics: {
        total: totalProposals,
        approved: approvedProposals.length,
        pending: pendingProposals.length,
        rejected: rejectedProposals.length,
      },
      recentBudgets,
      commissionByStatus,
      affiliateBudgets,
    };
  }, [budgets, proposals, profile?.id]);

  if (!commissionAnalytics) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className='text-center'>
          <AlertCircle className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
          <p className='text-muted-foreground'>
            Unable to load commission data
          </p>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'locked':
        return 'bg-yellow-100 text-yellow-800';
      case 'spent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Overview Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Commissions
            </CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(commissionAnalytics.totalCommissionEarned, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              From {commissionAnalytics.affiliateBudgets.length} projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Pending Commissions
            </CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(commissionAnalytics.pendingCommissions, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Awaiting project completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>This Month</CardTitle>
            <Calendar className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(commissionAnalytics.thisMonthCommissions, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Current month earnings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Conversion Rate
            </CardTitle>
            <Target className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {commissionAnalytics.conversionRate.toFixed(1)}%
            </div>
            <p className='text-xs text-muted-foreground'>
              {commissionAnalytics.proposalMetrics.approved} of{' '}
              {commissionAnalytics.proposalMetrics.total} proposals
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Commission Breakdown & Recent Activity */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        <Card>
          <CardHeader>
            <CardTitle>Commission Breakdown</CardTitle>
            <CardDescription>Commission status distribution</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Paid Commissions</span>
                <span className='font-medium'>
                  {formatCurrency(commissionAnalytics.paidCommissions, 'USD')}
                </span>
              </div>
              <Progress
                value={
                  (commissionAnalytics.paidCommissions /
                    commissionAnalytics.totalCommissionEarned) *
                  100
                }
                className='h-2'
              />
            </div>

            <div className='space-y-2'>
              <div className='flex justify-between text-sm'>
                <span>Pending Commissions</span>
                <span className='font-medium'>
                  {formatCurrency(
                    commissionAnalytics.pendingCommissions,
                    'USD'
                  )}
                </span>
              </div>
              <Progress
                value={
                  (commissionAnalytics.pendingCommissions /
                    commissionAnalytics.totalCommissionEarned) *
                  100
                }
                className='h-2'
              />
            </div>

            <div className='pt-4 border-t'>
              <div className='flex justify-between text-sm font-medium'>
                <span>Total Earned</span>
                <span>
                  {formatCurrency(
                    commissionAnalytics.totalCommissionEarned,
                    'USD'
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Commission Activity</CardTitle>
            <CardDescription>
              Latest commission-generating projects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {commissionAnalytics.recentBudgets.map((budget) => (
                <div
                  key={budget.id}
                  className='flex items-center justify-between'
                >
                  <div className='space-y-1'>
                    <div className='text-sm font-medium'>
                      Project Budget #{budget.id.slice(-8)}
                    </div>
                    <div className='text-xs text-muted-foreground'>
                      {formatDate(budget.created_at)} • {budget.Category}
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='text-sm font-medium'>
                      {formatCurrency(
                        budget.AffiliateCommission || 0,
                        budget.Currency
                      )}
                    </div>
                    <Badge className={getStatusColor(budget.Status)}>
                      {budget.Status}
                    </Badge>
                  </div>
                </div>
              ))}
              {commissionAnalytics.recentBudgets.length === 0 && (
                <div className='text-center py-4 text-muted-foreground'>
                  <Banknote className='h-8 w-8 mx-auto mb-2 opacity-50' />
                  <p className='text-sm'>No commission activity yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Proposal Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Proposal Performance</CardTitle>
          <CardDescription>
            Your proposal submission and approval metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-blue-600'>
                {commissionAnalytics.proposalMetrics.total}
              </div>
              <div className='text-sm text-muted-foreground'>
                Total Proposals
              </div>
            </div>

            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-green-600'>
                {commissionAnalytics.proposalMetrics.approved}
              </div>
              <div className='text-sm text-muted-foreground'>Approved</div>
            </div>

            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-yellow-600'>
                {commissionAnalytics.proposalMetrics.pending}
              </div>
              <div className='text-sm text-muted-foreground'>Pending</div>
            </div>

            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-red-600'>
                {commissionAnalytics.proposalMetrics.rejected}
              </div>
              <div className='text-sm text-muted-foreground'>Rejected</div>
            </div>
          </div>

          <div className='mt-6 space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>Approval Rate</span>
              <span>{commissionAnalytics.conversionRate.toFixed(1)}%</span>
            </div>
            <Progress
              value={commissionAnalytics.conversionRate}
              className='h-3'
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
