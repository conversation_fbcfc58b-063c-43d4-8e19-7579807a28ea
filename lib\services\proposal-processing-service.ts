import type {
  Budget,
  Client,
  CreateBudgetInput,
  CreateClientInput,
  CreateProjectInput,
  Project,
  Proposal,
  ProposalProcessingResult,
  ProposalValidation,
  ReferalProposalType,
} from '@/lib/supabase/database-modules';

/**
 * Comprehensive proposal processing service
 * Handles the complete workflow from proposal approval to client/project/budget creation
 */
export const ProposalProcessingService = {
  validateAndExtractProposalData: (proposal: Proposal): ProposalValidation => {
    const errors: string[] = [];
    const warnings: string[] = [];

    const proposalData: ReferalProposalType | null =
      proposal.affiliate_proposal;

    // Extract client data
    const clientData: Partial<CreateClientInput> = {
      name:
        proposal.client_name || proposalData?.client_name || 'Unknown Client',
      email: proposal.client_email || null,
      phone: proposal.client_phone || null,
      description: proposal.client_description || null,
      created_by: proposal.user_id,
    };

    // Extract project data
    const projectName =
      proposalData?.proposal_type || `Project for ${clientData.name}`;
    const projectData: Partial<CreateProjectInput> = {
      name: projectName,
      description: proposalData?.proposal_message || null,
      is_proposed: true,
      affiliate_user: proposal.user_id,
    };

    // Extract budget data - check both proposal.proposed_budget and proposalData.proposed_budget
    const budgetAmount =
      proposal.proposed_budget || proposalData?.proposed_budget || 0;

    // Use default dates for budget
    const startDate = new Date().toISOString();
    const endDate =
      ProposalProcessingService.calculateDefaultEndDate(undefined);

    const budgetData: Partial<CreateBudgetInput> = {
      ActualAmount: budgetAmount,
      CurrentAmount: budgetAmount,
      Currency: 'USD',
      Category:
        ProposalProcessingService.inferCategoryFromProposal(proposalData),
      Status: 'draft',
      has_affiliate: true,
      affiliateId: proposal.user_id,
      AffiliateCommission: budgetAmount > 0 ? budgetAmount * 0.1 : 0, // 10% commission if budget exists
      StartDate: startDate,
      EndDate: endDate,
      Notes: `Created from proposal ${proposal.id} - ${proposalData?.proposal_type || 'referral'}${budgetAmount > 0 ? ` (Budget: $${budgetAmount})` : ''}`,
    };

    // Validation rules
    if (!clientData.name) {
      errors.push('Client name is required');
    }

    if (budgetAmount && budgetAmount <= 0) {
      errors.push('Budget amount must be positive');
    }

    if (budgetData.StartDate && budgetData.EndDate) {
      const startDate = new Date(budgetData.StartDate);
      const endDate = new Date(budgetData.EndDate);
      if (endDate <= startDate) {
        errors.push('End date must be after start date');
      }
    }

    // Warnings for missing optional data
    if (!clientData.email) {
      warnings.push('No client email provided - communication may be limited');
    }

    if (!budgetAmount) {
      warnings.push(
        'No budget information provided - budget tracking will not be available'
      );
    }

    if (!projectName) {
      warnings.push(
        'No project name provided - project will need to be created manually'
      );
    }

    // Always add timeline warning for basic referral proposals
    warnings.push(
      'No timeline provided - using default 90-day project duration'
    );

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      extractedData: {
        clientData,
        projectData,
        budgetData,
      },
    };
  },

  processApprovedProposal: (
    proposal: Proposal,
    hooks: {
      addClient: (input: CreateClientInput) => Promise<Client>;
      fetchClient: (emailOrId: string) => Promise<Client | null>;
      addProject: (input: CreateProjectInput) => Promise<Project>;
      addBudget: (input: CreateBudgetInput) => Promise<Budget>;
    }
  ): Promise<ProposalProcessingResult> => {
    return new Promise((resolve, reject) => {
      console.log('🚀 Starting proposal processing for proposal:', {
        id: proposal.id,
        user_id: proposal.user_id,
        client_name: proposal.client_name,
        client_email: proposal.client_email,
        proposed_budget: proposal.proposed_budget,
        affiliate_proposal: proposal.affiliate_proposal,
      });

      const result: ProposalProcessingResult = {
        success: false,
        errors: [],
        warnings: [],
        summary: '',
      };

      // Step 1: Validate proposal data
      console.log('📋 Step 1: Validating proposal data...');
      const validation =
        ProposalProcessingService.validateAndExtractProposalData(proposal);
      result.warnings = validation.warnings;

      console.log('✅ Validation result:', {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        extractedData: validation.extractedData,
      });

      if (!validation.isValid) {
        console.error('❌ Validation failed:', validation.errors);
        result.errors = validation.errors;
        result.summary = `Validation failed: ${validation.errors.join(', ')}`;
        resolve(result);
        return;
      }

      const { clientData, projectData, budgetData } = validation.extractedData;

      // Step 2: Create or find existing client
      console.log('👤 Step 2: Processing client...', clientData);

      const processClient = () => {
        if (clientData.email) {
          console.log(
            '🔍 Checking for existing client with email:',
            clientData.email
          );
          hooks
            .fetchClient(clientData.email)
            .then((existingClient) => {
              if (existingClient) {
                console.log('✅ Found existing client:', {
                  id: existingClient.id,
                  name: existingClient.name,
                  email: existingClient.email,
                });
                result.client = existingClient;
                result.warnings.push(
                  `Using existing client: ${existingClient.name}`
                );
                processProject(existingClient);
              } else {
                console.log('➕ Creating new client with email...');
                hooks
                  .addClient(clientData as CreateClientInput)
                  .then((client) => {
                    console.log('✅ Created new client:', {
                      id: client.id,
                      name: client.name,
                      email: client.email,
                    });
                    result.client = client;
                    processProject(client);
                  })
                  .catch((error) => {
                    console.error(
                      '❌ Error creating client with email:',
                      error
                    );
                    result.errors.push(
                      `Failed to create client: ${error.message}`
                    );
                    result.summary = `Client creation failed: ${error.message}`;
                    resolve(result);
                  });
              }
            })
            .catch((error) => {
              console.error('❌ Error fetching existing client:', error);
              result.errors.push(
                `Failed to fetch existing client: ${error.message}`
              );
              result.summary = `Client lookup failed: ${error.message}`;
              resolve(result);
            });
        } else {
          console.log('➕ Creating new client without email...');
          hooks
            .addClient(clientData as CreateClientInput)
            .then((client) => {
              console.log('✅ Created new client without email:', {
                id: client.id,
                name: client.name,
              });
              result.client = client;
              processProject(client);
            })
            .catch((error) => {
              console.error('❌ Error creating client without email:', error);
              result.errors.push(`Failed to create client: ${error.message}`);
              result.summary = `Client creation failed: ${error.message}`;
              resolve(result);
            });
        }
      };

      // Step 3: Create project (always create a project for approved proposals)
      const processProject = (client: Client) => {
        console.log('🏗️ Step 3: Creating project...', projectData);
        const projectInput: CreateProjectInput = {
          ...projectData,
          client_id: client.id,
        } as CreateProjectInput;

        console.log('🏗️ Creating project with input:', projectInput);
        hooks
          .addProject(projectInput)
          .then((project) => {
            console.log('✅ Created project:', {
              id: project.id,
              name: project.name,
              budget_id: project.budget_id,
            });
            result.project = project;
            processBudget(client, project);
          })
          .catch((error) => {
            console.error('❌ Error creating project:', error);
            result.errors.push(`Failed to create project: ${error.message}`);
            result.summary = `Project creation failed: ${error.message}`;
            resolve(result);
          });
      };

      // Step 4: Handle budget creation/update
      const processBudget = (client: Client, project: Project) => {
        console.log('💰 Step 4: Processing budget...', {
          project_budget_id: project.budget_id,
          proposed_amount: budgetData.ActualAmount,
        });

        if (project.budget_id) {
          // Budget was automatically created by database trigger
          const proposedAmount = budgetData.ActualAmount || 0;
          console.log(
            '✅ Budget auto-created by trigger, proposed amount:',
            proposedAmount
          );

          if (proposedAmount > 0) {
            result.warnings.push(
              `Budget auto-created by trigger. Proposed amount: $${proposedAmount} (Note: Budget update functionality needs to be implemented)`
            );
          } else {
            result.warnings.push(
              'Budget was automatically created by database trigger with default values'
            );
          }
          finalizeSummary(client, project);
        } else if (budgetData.ActualAmount && budgetData.ActualAmount > 0) {
          // Fallback: Create budget manually if no automatic budget was created
          console.log('💰 Creating budget manually...', budgetData);
          const budgetInput: CreateBudgetInput = {
            ...budgetData,
            ProjectId: project.id,
            ClientId: client.id,
          } as CreateBudgetInput;

          hooks
            .addBudget(budgetInput)
            .then((budget) => {
              console.log('✅ Created budget:', {
                id: budget.id,
                amount: budget.ActualAmount,
                currency: budget.Currency,
              });
              result.budget = budget;
              finalizeSummary(client, project);
            })
            .catch((error) => {
              console.error('❌ Error creating budget:', error);
              result.errors.push(`Failed to create budget: ${error.message}`);
              result.summary = `Budget creation failed: ${error.message}`;
              resolve(result);
            });
        } else {
          console.log('ℹ️ No budget amount provided, skipping budget creation');
          finalizeSummary(client, project);
        }
      };

      // Finalize and generate summary
      const finalizeSummary = (client: Client, project: Project) => {
        console.log('📊 Finalizing summary...');

        const proposedAmount = budgetData.ActualAmount || 0;
        const summaryParts = [
          `✅ Client: ${client.name}`,
          result.project ? `✅ Project: ${result.project.name}` : '',
          result.budget
            ? `✅ Budget: ${budgetData.Currency}${result.budget.ActualAmount}`
            : project.budget_id && proposedAmount > 0
              ? `✅ Budget: Auto-created with proposed amount $${proposedAmount}`
              : project.budget_id
                ? `✅ Budget: Auto-created (default amount)`
                : '',
        ].filter(Boolean);

        result.success = true;
        result.summary = summaryParts.join('\n');

        console.log('🎉 Proposal processing completed successfully:', {
          client_id: client.id,
          project_id: project.id,
          budget_id: result.budget?.id || project.budget_id,
          summary: result.summary,
          warnings: result.warnings,
        });

        resolve(result);
      };

      // Start the processing chain
      processClient();
    });
  },

  inferCategoryFromProposal: (
    proposalData: ReferalProposalType | null
  ): 'marketing' | 'development' | 'consulting' | 'operations' | 'other' => {
    const text = (
      proposalData?.proposal_message ||
      proposalData?.proposal_type ||
      ''
    ).toLowerCase();

    if (
      text.includes('marketing') ||
      text.includes('advertising') ||
      text.includes('promotion')
    ) {
      return 'marketing';
    }
    if (
      text.includes('development') ||
      text.includes('coding') ||
      text.includes('programming') ||
      text.includes('website') ||
      text.includes('app')
    ) {
      return 'development';
    }
    if (
      text.includes('consulting') ||
      text.includes('advice') ||
      text.includes('strategy') ||
      text.includes('planning')
    ) {
      return 'consulting';
    }
    if (
      text.includes('operations') ||
      text.includes('management') ||
      text.includes('process')
    ) {
      return 'operations';
    }

    return 'other';
  },

  calculateDefaultEndDate: (timeline?: string): string => {
    const now = new Date();
    let daysToAdd = 90; // Default 90 days

    if (timeline) {
      const timelineText = timeline.toLowerCase();
      if (timelineText.includes('week')) {
        const weeks =
          ProposalProcessingService.extractNumber(timelineText) || 4;
        daysToAdd = weeks * 7;
      } else if (timelineText.includes('month')) {
        const months =
          ProposalProcessingService.extractNumber(timelineText) || 3;
        daysToAdd = months * 30;
      } else if (timelineText.includes('day')) {
        daysToAdd = ProposalProcessingService.extractNumber(timelineText) || 90;
      }
    }

    const endDate = new Date(now.getTime() + daysToAdd * 24 * 60 * 60 * 1000);
    return endDate.toISOString().split('T')[0];
  },

  extractNumber: (text: string): number | null => {
    const match = text.match(/\d+/);
    return match ? parseInt(match[0], 10) : null;
  },

  generateProcessingReport: (
    proposal: Proposal,
    result: ProposalProcessingResult
  ): string => {
    const report = [
      `=== Proposal Processing Report ===`,
      `Proposal ID: ${proposal.id}`,
      `Client: ${proposal.client_name}`,
      `Affiliate: ${proposal.user_email}`,
      `Status: ${result.success ? 'SUCCESS' : 'FAILED'}`,
      ``,
      `Results:`,
      result.client
        ? `✅ Client Created: ${result.client.name} (${result.client.id})`
        : '❌ No client created',
      result.project
        ? `✅ Project Created: ${result.project.name} (${result.project.id})`
        : '⚠️ No project created',
      result.budget
        ? `✅ Budget Created: ${result.budget.Currency}${result.budget.ActualAmount} (${result.budget.id})`
        : '⚠️ No budget created',
      ``,
      result.warnings.length > 0
        ? `Warnings:\n${result.warnings.map((w) => `⚠️ ${w}`).join('\n')}`
        : '',
      result.errors.length > 0
        ? `Errors:\n${result.errors.map((e) => `❌ ${e}`).join('\n')}`
        : '',
      ``,
      `Summary: ${result.summary}`,
      `=================================`,
    ]
      .filter(Boolean)
      .join('\n');

    return report;
  },
};
