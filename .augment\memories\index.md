# Preferences
- User prefers using the Thehuefactory Supabase MCP tool for database operations and wants database types to be updated automatically when making schema changes.
- User wants missing Zustand stores to be created in the lib/store directory while working on components.
- User prefers using database hooks in use-db instead of API routes for fetching labels, priorities, projects, and statuses data in create-new-issue components.
- User prefers not to test features during implementation when explicitly requested.
- User prefers to keep existing backend implementations when updating projects and wants to use Thehuefactory Supabase MCP tool for adding database tables during migrations.
- User prefers centralizing all database types in lib/supabase/database-modules.ts (referencing base types from database-types.ts) and following the useIssues hook pattern for all database hooks with full CRUD operations.
- User prefers using ScrollArea component instead of overflow-y for scrolling and wants status colors/icons replaced with <StatusIcon statusId={selectedItem.id} /> component throughout the application.
- User prefers comprehensive table implementations that reuse existing UI components, follow database hooks architecture with Promise-based .then() chains, implement StatusIcon-style visual indicators, and break down complex features into structured tasks using task management tools.
- User prefers showing user avatar and full name instead of user ID in proposal dialogs, and wants to prevent approval status changes once a proposal has been approved or rejected.
- User requires ScrollArea with min-h-[calc(100vh-theme(spacing.24))] for mobile table optimization.
- User prefers replacing custom table implementations with shadcn/ui table components and wants to use OriginUI table patterns (specifically comp-471.json).
- User prefers using shadcn's data-table approach (https://ui.shadcn.com/docs/components/data-table) for table implementations instead of custom table components.
- User prefers organizing all table components in a components/tables/ folder structure, moving reusable data-table.tsx to components/ui/, and standardizing all tables to use the shadcn data-table approach with columns definitions, sorting, filtering, and pagination.
- User prefers having metrics sections (similar to applications metrics) added to projects, client-requests (proposals), and members pages for consistent dashboard experience.
- User requires members management system to follow exact patterns from applications/projects/proposals: Promise-based .then() chains (not async/await), standardized metrics components with specific skeleton loading pattern using metric name arrays, shadcn data-table approach, consistent page layouts with metrics above tables, and real-time Supabase subscriptions with optimistic updates.
- User prefers member status to be read-only in admin interface (only changeable by users themselves), wants member detail pages with comprehensive information including projects/issues/teams, and prefers removing edit actions from dropdowns in favor of dedicated detail pages.
- User prefers Promise-based toast notification patterns using .then() chains (not async/await) for CRUD operations, with success/error/loading toast types for all database operations.
- User prefers comprehensive team management systems with metrics sections, shadcn data-table approach, CRUD operations, project member integration, and structured task breakdown using task management tools for complex implementations.
- User requires systematic type error auditing and fixing using proper type assertions and interface updates rather than 'any' types, following established codebase patterns for type handling.
- When creating a new project and selecting an affiliate, show only users with affiliate role (not all users).
- For affiliate selection, use the same UI component pattern that we use for selecting the project lead (not a generic select dropdown)
- User prefers consolidating functionality into existing hooks rather than creating separate ones.
- User wants proposal forms with two-section structure (client details + proposal details).
- User prefers using priority-style UI components instead of select dropdowns for type selection.
- User requires comprehensive affiliate portfolio system with database integration between profiles and JOIN_US_TABLE, mandatory NDA access control using is_nda_signed field, Supabase Storage for image management with automatic deletion, and functional metrics based on real referral data.
- When linking profiles table with JOIN_US_TABLE, use a users_id column in profiles to match with user_id in JOIN_US_TABLE rather than matching profiles.id directly, and update the onApprovedSubmit function to populate this relationship when creating auth accounts.
- User requires all database hooks to use Promise-based .then() chains with resolve/reject patterns instead of async/await syntax, following the established architectural pattern used throughout the codebase.
- User prefers collaborator dashboard systems with permission-based field restrictions (can edit status/deadline/progress/notes but not name/description/priority/members), component reuse from admin pages with collaborator-specific filtering, and extending existing database hooks with role-specific methods like getCollaboratorProjects() and checkProjectAccess().
- User requires collaborator dashboards to have strict permission restrictions where collaborators cannot add users/members to projects or issues, can only assign from existing project members, and should use field-level edit restrictions rather than page-level blocking.
- User prefers streamlined collaborator dashboards where edit functionality is integrated directly into table rows rather than separate edit pages, and wants issue/task creation accessible from project rows using modal dialogs that reuse existing admin components.
- User requires consistent admin interface design patterns with metrics sections and skeleton loading.
- User requires currency utility modules for multi-currency budget systems with formatting and conversion capabilities.
- User requires email utility modules in services/ directory using Promise-based .then() chains (not async/await), comprehensive console logging for email tracking, and structured task management tools for multi-part email system implementations.
- User prefers server-side auth account creation using PostgreSQL functions/triggers instead of client-side React code for better permissions and centralized user creation logic.

# Code Style
- When creating utility functions, always use regular objects with methods instead of classes with only static methods - prefer const Utils = { method: () => {} } over class Utils { static method() {} }
- When refactoring components, always implement the functionality first before adding imports - add imports only after the implementation is complete and working.
- Use React Hook Form and the Form component for all forms instead of manual state management.
- Always use Zod with React Hook Forms for validation and schema definition.
- All onSubmit functions should use Promise with resolve/reject pattern using .then() chains instead of async/await.
- Never use "rounded" CSS classes in this project

# Error Handling
- User requires proper error handling with meaningful messages instead of empty objects.

# Architecture
- User requires Promise-based .then() chains instead of async/await throughout the codebase.

# Task Management
- User prefers comprehensive task management with structured phases for complex implementations.
- User requires systematic task breakdown using task management tools for complex multi-part implementations, and prefers comprehensive console logging throughout approval workflows to track each step of the process including data transformations and success/failure states.

# Admin Interface
- User requires shadcn data-table approach for all admin tables.
- Always use proper icon components (StatusIcon, PriorityIcon, proper label icons) instead of colored circles or generic icons across all admin and collaborator interfaces for consistent UI patterns.
- In this project, status and priorities have dedicated icon components that should be used instead of colored circles - find and use these icon components for consistent UI patterns.

# Accessibility
- Always enforce accessibility by adding keyboard event handlers (onKeyDown with Enter/Space keys) to any clickable elements that use onClick, and use proper semantic HTML elements like button instead of adding role="button" to non-interactive elements.