import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { z } from 'zod';
import { ReviewedApplication } from '@/lib/emails/volunteers';
import type { Database } from '@/lib/supabase/database-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

type dataProps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export async function POST(request: Request) {
  const form: dataProps = await request.json();
  console.log('📧 API ROUTE: Volunteer reviewed email request received:', {
    applicationId: form.id,
    email: form.email,
    fullName: form.full_name,
    role: form.join_role,
    hasResendKey: !!process.env.NEXT_PUBLIC_RESEND_API_KEY,
  });
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: form.email ? [form.email] : [],
      bcc: ['<EMAIL>'],
      subject: 'Your Application Has Been Reviewed.',
      text: '',
      react: ReviewedApplication(form),
    });
    console.log('✅ API ROUTE: Volunteer reviewed email sent successfully:', {
      emailId: data.data?.id,
      applicationId: form.id,
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.error('❌ API ROUTE: Volunteer reviewed email failed:', error);
    return NextResponse.json({ error });
  }
}
