import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import type { ProposalRejectedEmailData } from '@/lib/types/email-types';

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const ProposalRejected = (data: ProposalRejectedEmailData) => {
  const { proposal, affiliate, triggeredBy, rejectionDate, reason } = data;
  
  return (
    <Html>
      <Head />
      <Preview>Update on your proposal submission</Preview>
      <Body style={main}>
        {/* Header Section */}
        <Container
          style={{
            ...container,
            backgroundColor: colors['100'],
          }}
        >
          <Img
            src={`${baseUrl}/thehuefactory_hero.png`}
            width='100%'
            height='auto'
            alt='Email Header Image'
          />
        </Container>
        
        {/* Header Transition */}
        <Container
          style={{
            margin: '0 auto',
            backgroundColor: colors['100'],
            alignItems: 'center',
            alignContent: 'center',
            textAlign: 'center',
          }}
        >
          <Section
            style={{
              backgroundColor: 'white',
              height: 20,
              borderTopLeftRadius: '16px',
              borderTopRightRadius: '16px',
            }}
          ></Section>
        </Container>

        {/* Main Content */}
        <Container style={container}>
          <Heading style={h1}>Proposal Update</Heading>
          
          <Text style={{ ...text, marginBottom: '24px' }}>
            Thank you for your proposal submission. After careful consideration, 
            we've made a decision regarding your project proposal.
          </Text>

          {/* Proposal Details */}
          <Container style={proposalCard}>
            <Heading style={proposalTitle}>{proposal.title}</Heading>
            
            {proposal.description && (
              <Text style={{ ...text, marginBottom: '16px' }}>
                {proposal.description}
              </Text>
            )}

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Client:</Text>
              <Text style={detailValue}>{proposal.client_name}</Text>
            </Container>

            {proposal.budget && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Budget:</Text>
                <Text style={detailValue}>${proposal.budget.toLocaleString()}</Text>
              </Container>
            )}

            {proposal.timeline && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Timeline:</Text>
                <Text style={detailValue}>{proposal.timeline}</Text>
              </Container>
            )}

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Review Date:</Text>
              <Text style={detailValue}>
                {new Date(rejectionDate).toLocaleDateString()}
              </Text>
            </Container>

            {affiliate && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Affiliate:</Text>
                <Text style={detailValue}>{affiliate.full_name}</Text>
              </Container>
            )}

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Reviewed by:</Text>
              <Text style={detailValue}>{triggeredBy.full_name}</Text>
            </Container>

            {/* Status Badge */}
            <Container style={{ textAlign: 'center', marginTop: '20px' }}>
              <Container style={statusBadge}>
                <Text style={statusText}>NOT APPROVED</Text>
              </Container>
            </Container>
          </Container>

          {/* Reason Section */}
          {reason && (
            <Container style={reasonCard}>
              <Heading style={reasonTitle}>Feedback</Heading>
              <Text style={{ ...text, marginBottom: '16px' }}>
                {reason}
              </Text>
            </Container>
          )}

          <Text style={{ ...text, marginBottom: '24px' }}>
            While this particular proposal wasn't selected, we appreciate your interest 
            in working with thehuefactory. We encourage you to submit future proposals 
            that align with our current project needs and capabilities.
          </Text>

          <Text style={{ ...text, marginBottom: '24px' }}>
            We value our relationship with our affiliates and look forward to 
            potential collaboration opportunities in the future.
          </Text>

          {/* Action Button */}
          <Container style={{ textAlign: 'center', marginTop: '32px' }}>
            <Link
              href={`https://team.thehuefactory.co/admin/proposals/${proposal.id}`}
              target='_blank'
              style={{
                backgroundColor: 'transparent',
                border: `2px solid ${colors[100]}`,
                paddingRight: 18,
                paddingLeft: 18,
                fontWeight: 'bold',
                color: colors[100],
                paddingTop: 14,
                paddingBottom: 14,
                borderRadius: 32,
                whiteSpace: 'nowrap',
                fontFamily: 'monospace',
                textDecoration: 'none',
                display: 'inline-block',
              }}
            >
              View Proposal Details
            </Link>
          </Container>

          <Text style={{ ...text, marginBottom: '24px', marginTop: '32px' }}>
            If you have any questions about this decision or would like to discuss 
            future opportunities, please don't hesitate to{' '}
            <Link
              href='https://www.thehuefactory.co/contact'
              target='_blank'
              style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            >
              contact us
            </Link>{' '}
            or reply to this email.
          </Text>
        </Container>

        {/* Footer Section */}
        <Container
          style={{
            ...container,
            marginTop: '48px',
          }}
        >
          <Img
            src={`${baseUrl}/Logo_3dicon_orange.png`}
            width='42'
            height='42'
            alt="thehuefactory's Logo"
          />
          <Text style={{ ...footer, marginTop: '40px' }}>
            <Link
              href='https://www.thehuefactory.co/'
              target='_blank'
              style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            >
              thehuefactory.co
            </Link>{' '}
            <br />
            The Creative Powerhouse.
            <br />
            Copyright © 2024 thehuefactory. All rights reserved.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default ProposalRejected;

// Styles following existing pattern
const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const proposalCard = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  padding: '24px',
  marginBottom: '24px',
};

const proposalTitle = {
  color: colors[100],
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const detailsContainer = {
  display: 'flex',
  marginBottom: '8px',
  alignItems: 'center',
};

const detailLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
  minWidth: '120px',
};

const detailValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '0',
};

const statusBadge = {
  display: 'inline-block',
  backgroundColor: '#6c757d',
  padding: '12px 24px',
  borderRadius: '25px',
  border: '2px solid #495057',
};

const statusText = {
  color: 'white',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
  letterSpacing: '1px',
};

const reasonCard = {
  backgroundColor: '#fff3cd',
  border: '1px solid #ffeaa7',
  borderRadius: '8px',
  padding: '20px',
  marginBottom: '24px',
};

const reasonTitle = {
  color: '#856404',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 12px 0',
  padding: '0',
};
