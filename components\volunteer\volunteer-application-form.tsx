'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Send } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

const volunteerApplicationSchema = z.object({
  skills: z.string().min(1, 'Please list your skills'),
  availability: z.string().min(1, 'Please describe your availability'),
  experience: z.string().min(10, 'Please provide at least 10 characters describing your experience'),
  motivation: z.string().min(10, 'Please provide at least 10 characters describing your motivation'),
  preferredProjects: z.string().min(1, 'Please specify your preferred project types'),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
  agreeToNDA: z.boolean().refine(val => val === true, 'You must agree to sign the NDA when required'),
});

type VolunteerApplicationFormData = z.infer<typeof volunteerApplicationSchema>;

interface VolunteerApplicationFormProps {
  initialData?: Partial<VolunteerApplicationFormData>;
  onSubmit?: (data: VolunteerApplicationFormData) => Promise<void>;
  isSubmitting?: boolean;
  submitButtonText?: string;
}

export function VolunteerApplicationForm({
  initialData,
  onSubmit,
  isSubmitting = false,
  submitButtonText = 'Submit Application'
}: VolunteerApplicationFormProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<VolunteerApplicationFormData>({
    resolver: zodResolver(volunteerApplicationSchema),
    defaultValues: {
      skills: initialData?.skills || '',
      availability: initialData?.availability || '',
      experience: initialData?.experience || '',
      motivation: initialData?.motivation || '',
      preferredProjects: initialData?.preferredProjects || '',
      agreeToTerms: initialData?.agreeToTerms || false,
      agreeToNDA: initialData?.agreeToNDA || false,
    },
  });

  const handleSubmit = (data: VolunteerApplicationFormData) => {
    return new Promise<void>((resolve, reject) => {
      setIsLoading(true);
      
      if (onSubmit) {
        onSubmit(data)
          .then(() => {
            toast.success('Application submitted successfully!');
            resolve();
          })
          .catch((error) => {
            console.error('Error submitting application:', error);
            toast.error('Failed to submit application. Please try again.');
            reject(error);
          })
          .finally(() => {
            setIsLoading(false);
          });
      } else {
        // Mock submission for demo
        setTimeout(() => {
          toast.success('Application submitted successfully!');
          setIsLoading(false);
          resolve();
        }, 2000);
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Volunteer Application</CardTitle>
        <p className='text-sm text-muted-foreground'>
          Tell us about yourself and why you'd like to volunteer with us.
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            {/* Skills */}
            <FormField
              control={form.control}
              name='skills'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Technical Skills *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., React, TypeScript, Python, UI/UX Design'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    List your technical skills separated by commas
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Availability */}
            <FormField
              control={form.control}
              name='availability'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Availability *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., 10-15 hours per week, weekends preferred'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    How much time can you commit to volunteering?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Experience */}
            <FormField
              control={form.control}
              name='experience'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Relevant Experience *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Describe your relevant experience and background...'
                      className='min-h-[120px]'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Tell us about your professional or personal experience that's relevant to volunteering
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Motivation */}
            <FormField
              control={form.control}
              name='motivation'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Motivation for Volunteering *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Why do you want to volunteer with us?'
                      className='min-h-[120px]'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    What motivates you to volunteer and contribute to our projects?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Preferred Projects */}
            <FormField
              control={form.control}
              name='preferredProjects'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preferred Project Types *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., Web Development, Mobile Apps, Data Analysis'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    What types of projects are you most interested in working on?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Terms Agreement */}
            <FormField
              control={form.control}
              name='agreeToTerms'
              render={({ field }) => (
                <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className='space-y-1 leading-none'>
                    <FormLabel>
                      I agree to the terms and conditions *
                    </FormLabel>
                    <FormDescription>
                      By checking this box, you agree to follow our volunteer guidelines and code of conduct.
                    </FormDescription>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            {/* NDA Agreement */}
            <FormField
              control={form.control}
              name='agreeToNDA'
              render={({ field }) => (
                <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className='space-y-1 leading-none'>
                    <FormLabel>
                      I agree to sign an NDA when required *
                    </FormLabel>
                    <FormDescription>
                      Some projects may require signing a Non-Disclosure Agreement to protect confidential information.
                    </FormDescription>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <div className='flex justify-end pt-4'>
              <Button 
                type='submit' 
                disabled={isLoading || isSubmitting}
                className='flex items-center gap-2'
              >
                <Send className='h-4 w-4' />
                {isLoading || isSubmitting ? 'Submitting...' : submitButtonText}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Simplified version for quick updates
export function VolunteerApplicationUpdateForm({
  initialData,
  onSubmit,
}: {
  initialData: Partial<VolunteerApplicationFormData>;
  onSubmit: (data: Partial<VolunteerApplicationFormData>) => Promise<void>;
}) {
  return (
    <VolunteerApplicationForm
      initialData={initialData}
      onSubmit={onSubmit}
      submitButtonText='Update Application'
    />
  );
}
