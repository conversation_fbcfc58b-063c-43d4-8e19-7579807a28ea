'use client';

import { <PERSON>, CheckCircle, Clock, Mail, MessageSquare } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Notification {
  id: string;
  type: 'assignment' | 'comment' | 'status_change' | 'deadline' | 'mention';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high';
}

export default function VolunteerInboxPage() {
  // Mock notifications - in real implementation, this would come from database
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'assignment',
      title: 'New Issue Assigned',
      message: 'You have been assigned to issue "Fix navigation bug" in the Community Website project.',
      timestamp: '2024-01-20T10:30:00Z',
      read: false,
      actionUrl: '/volunteer/issues/123',
      priority: 'high',
    },
    {
      id: '2',
      type: 'comment',
      title: 'New Comment',
      message: 'Sarah Johnson commented on issue "Update user profile page".',
      timestamp: '2024-01-20T09:15:00Z',
      read: false,
      actionUrl: '/volunteer/issues/124',
      priority: 'medium',
    },
    {
      id: '3',
      type: 'status_change',
      title: 'Issue Status Updated',
      message: 'Issue "Implement search functionality" has been moved to "In Review".',
      timestamp: '2024-01-19T16:45:00Z',
      read: true,
      actionUrl: '/volunteer/issues/125',
      priority: 'low',
    },
    {
      id: '4',
      type: 'deadline',
      title: 'Deadline Reminder',
      message: 'Issue "Mobile responsive design" is due in 2 days.',
      timestamp: '2024-01-19T14:20:00Z',
      read: false,
      actionUrl: '/volunteer/issues/126',
      priority: 'high',
    },
    {
      id: '5',
      type: 'mention',
      title: 'You were mentioned',
      message: 'Alex mentioned you in a comment on "Database optimization" issue.',
      timestamp: '2024-01-19T11:30:00Z',
      read: true,
      actionUrl: '/volunteer/issues/127',
      priority: 'medium',
    },
  ]);

  const unreadCount = notifications.filter(n => !n.read).length;
  const unreadNotifications = notifications.filter(n => !n.read);
  const readNotifications = notifications.filter(n => n.read);

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'assignment':
        return <Bell className='h-4 w-4' />;
      case 'comment':
        return <MessageSquare className='h-4 w-4' />;
      case 'status_change':
        return <CheckCircle className='h-4 w-4' />;
      case 'deadline':
        return <Clock className='h-4 w-4' />;
      case 'mention':
        return <Mail className='h-4 w-4' />;
      default:
        return <Bell className='h-4 w-4' />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const NotificationItem = ({ notification }: { notification: Notification }) => (
    <div 
      className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-muted/50 ${
        !notification.read ? 'bg-blue-50/50 dark:bg-blue-900/10 border-blue-200 dark:border-blue-800' : ''
      }`}
      onClick={() => {
        if (!notification.read) {
          markAsRead(notification.id);
        }
        if (notification.actionUrl) {
          window.location.href = notification.actionUrl;
        }
      }}
    >
      <div className='flex items-start gap-3'>
        <div className={`p-2 rounded-full ${getPriorityColor(notification.priority)}`}>
          {getNotificationIcon(notification.type)}
        </div>
        <div className='flex-1 min-w-0'>
          <div className='flex items-center justify-between mb-1'>
            <p className={`text-sm font-medium ${!notification.read ? 'text-foreground' : 'text-muted-foreground'}`}>
              {notification.title}
            </p>
            <div className='flex items-center gap-2'>
              <Badge variant='outline' className={`text-xs ${getPriorityColor(notification.priority)}`}>
                {notification.priority}
              </Badge>
              <span className='text-xs text-muted-foreground'>
                {formatTimestamp(notification.timestamp)}
              </span>
            </div>
          </div>
          <p className='text-sm text-muted-foreground'>
            {notification.message}
          </p>
          {!notification.read && (
            <div className='w-2 h-2 bg-blue-500 rounded-full mt-2' />
          )}
        </div>
      </div>
    </div>
  );

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-2'>
          <Mail className='h-4 w-4' />
          <p className='font-medium text-sm'>Inbox</p>
          {unreadCount > 0 && (
            <Badge variant='destructive' className='text-xs'>
              {unreadCount}
            </Badge>
          )}
        </div>
        {unreadCount > 0 && (
          <Button variant='outline' size='sm' onClick={markAllAsRead}>
            Mark all as read
          </Button>
        )}
      </header>
      
      <section className='flex-1 p-6'>
        <Tabs defaultValue='all' className='h-full flex flex-col'>
          <TabsList className='grid w-full grid-cols-3 mb-6'>
            <TabsTrigger value='all'>
              All ({notifications.length})
            </TabsTrigger>
            <TabsTrigger value='unread'>
              Unread ({unreadCount})
            </TabsTrigger>
            <TabsTrigger value='read'>
              Read ({readNotifications.length})
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value='all' className='flex-1'>
            <ScrollArea className='h-full'>
              <div className='space-y-4'>
                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <NotificationItem key={notification.id} notification={notification} />
                  ))
                ) : (
                  <div className='text-center py-12'>
                    <Mail className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
                    <p className='text-muted-foreground'>No notifications yet</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value='unread' className='flex-1'>
            <ScrollArea className='h-full'>
              <div className='space-y-4'>
                {unreadNotifications.length > 0 ? (
                  unreadNotifications.map((notification) => (
                    <NotificationItem key={notification.id} notification={notification} />
                  ))
                ) : (
                  <div className='text-center py-12'>
                    <CheckCircle className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
                    <p className='text-muted-foreground'>All caught up!</p>
                    <p className='text-sm text-muted-foreground'>No unread notifications</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value='read' className='flex-1'>
            <ScrollArea className='h-full'>
              <div className='space-y-4'>
                {readNotifications.length > 0 ? (
                  readNotifications.map((notification) => (
                    <NotificationItem key={notification.id} notification={notification} />
                  ))
                ) : (
                  <div className='text-center py-12'>
                    <Mail className='h-12 w-12 mx-auto mb-4 text-muted-foreground' />
                    <p className='text-muted-foreground'>No read notifications</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </section>
    </main>
  );
}
