// Base types for email system
export interface BaseEmailData {
  timestamp: string;
  triggeredBy: UserData;
  recipients: string[];
}

export interface UserData {
  id: string;
  full_name: string;
  email: string;
  avatar_url?: string;
  role?: string;
}

export interface StatusData {
  id: string;
  name: string;
  color: string;
  icon_name?: string;
}

export interface PriorityData {
  id: string;
  name: string;
  icon_name?: string;
  sort_order?: number;
}

export interface HealthData {
  id: string;
  name: string;
  color: string;
  description?: string;
}

// Issue-related email data types
export interface IssueEmailData extends BaseEmailData {
  issue: {
    id: string;
    title: string;
    description?: string;
    status?: StatusData;
    priority?: PriorityData;
    health?: HealthData;
    assignee?: UserData;
    due_date?: string;
  };
  project: {
    id: string;
    name: string;
    description?: string;
  };
}

export interface IssueCreatedEmailData extends IssueEmailData {
  actionType: 'created';
}

export interface IssueStatusUpdateEmailData extends IssueEmailData {
  actionType: 'status_changed';
  oldStatus: StatusData;
  newStatus: StatusData;
}

export interface IssuePriorityChangeEmailData extends IssueEmailData {
  actionType: 'priority_changed';
  oldPriority: PriorityData;
  newPriority: PriorityData;
}

export interface IssueAssignmentEmailData extends IssueEmailData {
  actionType: 'assigned';
  oldAssignee?: UserData;
  newAssignee?: UserData;
}

export interface IssueHealthUpdateEmailData extends IssueEmailData {
  actionType: 'health_changed';
  oldHealth: HealthData;
  newHealth: HealthData;
}

// Project-related email data types
export interface ProjectEmailData extends BaseEmailData {
  project: {
    id: string;
    name: string;
    description?: string;
    status?: StatusData;
    priority?: PriorityData;
    health?: HealthData;
    lead?: UserData;
    start_date?: string;
    target_date?: string;
    percent_complete?: number;
  };
}

export interface ProjectMemberAddedEmailData extends ProjectEmailData {
  actionType: 'member_added';
  member: UserData;
  role: string;
}

export interface ProjectStatusUpdateEmailData extends ProjectEmailData {
  actionType: 'status_changed';
  oldStatus: StatusData;
  newStatus: StatusData;
}

export interface ProjectPriorityChangeEmailData extends ProjectEmailData {
  actionType: 'priority_changed';
  oldPriority: PriorityData;
  newPriority: PriorityData;
}

export interface ProjectHealthUpdateEmailData extends ProjectEmailData {
  actionType: 'health_changed';
  oldHealth: HealthData;
  newHealth: HealthData;
}

// Proposal-related email data types
export interface ProposalEmailData extends BaseEmailData {
  proposal: {
    id: string;
    title: string;
    description?: string;
    client_name?: string;
    client_email?: string;
    budget?: number;
    timeline?: string;
    status?: string;
    is_approved?: boolean | null;
  };
  affiliate?: UserData;
}

export interface ProposalApprovedEmailData extends ProposalEmailData {
  actionType: 'approved';
  approvalDate: string;
  nextSteps?: string;
}

export interface ProposalRejectedEmailData extends ProposalEmailData {
  actionType: 'rejected';
  rejectionDate: string;
  reason?: string;
}

export interface ProposalStatusUpdateEmailData extends ProposalEmailData {
  actionType: 'status_changed';
  oldStatus: string;
  newStatus: string;
}

// Team-related email data types
export interface TeamEmailData extends BaseEmailData {
  team: {
    id: string;
    name: string;
    description?: string;
    icon?: string;
    color?: string;
  };
}

export interface TeamMemberAddedEmailData extends TeamEmailData {
  actionType: 'member_added';
  member: UserData;
  role?: string;
}

export interface TeamMemberRemovedEmailData extends TeamEmailData {
  actionType: 'member_removed';
  member: UserData;
  reason?: string;
}

export interface TeamRoleChangeEmailData extends TeamEmailData {
  actionType: 'role_changed';
  member: UserData;
  oldRole: string;
  newRole: string;
}

// Generic status update type for reusable components
export interface StatusUpdateEmailData extends BaseEmailData {
  entity: 'issue' | 'project' | 'proposal' | 'team';
  item: {
    id: string;
    title?: string;
    name?: string;
  };
  oldStatus: StatusData | string;
  newStatus: StatusData | string;
  project?: {
    id: string;
    name: string;
  };
}

// Email template props types
export interface EmailTemplateProps {
  data: BaseEmailData;
  baseUrl?: string;
  colors?: {
    primary: string;
    secondary: string;
    accent: string;
    dark: string;
  };
}

// API route data types
export interface EmailApiRequest {
  emailData: BaseEmailData;
  templateType: string;
  subject: string;
  recipients?: string[];
  bcc?: string[];
}

export interface EmailApiResponse {
  success: boolean;
  data?: any;
  error?: any;
  messageId?: string;
}

// Notification event types for routing
export type NotificationEventType = 
  | 'issue_created'
  | 'issue_status_updated'
  | 'issue_priority_changed'
  | 'issue_assigned'
  | 'issue_health_updated'
  | 'project_member_added'
  | 'project_status_updated'
  | 'project_priority_changed'
  | 'project_health_updated'
  | 'proposal_approved'
  | 'proposal_rejected'
  | 'proposal_status_updated'
  | 'team_member_added'
  | 'team_member_removed'
  | 'team_role_changed';

// Email preferences (for future implementation)
export interface EmailPreference {
  id: string;
  user_id: string;
  notification_type: NotificationEventType;
  enabled: boolean;
  created_at: string;
  updated_at: string;
}
