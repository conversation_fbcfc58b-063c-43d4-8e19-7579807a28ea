'use client';

import { ArrowLeft, Plus } from 'lucide-react';
import Link from 'next/link';
import { WaitlistsMetrics } from '@/components/database/waitlists-metrics';
import { WaitlistsDataTable } from '@/components/tables/database/waitlists-data-table';
import { Button } from '@/components/ui/button';
import { useWaitlists } from '@/hooks/use-db';

export default function DatabaseWaitlistsPage() {
  const { waitlists, loading } = useWaitlists();

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center space-x-4'>
          <Link href='/admin/database'>
            <Button variant='ghost' size='sm' className='h-8'>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Database
            </Button>
          </Link>
          <p className='font-medium text-sm'>Database - Waitlists</p>
        </div>
        <Link href='/admin/applications'>
          <Button size='sm' className='h-8'>
            <Plus className='h-4 w-4 mr-2' />
            View Applications
          </Button>
        </Link>
      </header>
      <section className='flex-1 p-6 space-y-6'>
        <WaitlistsMetrics waitlists={waitlists} loading={loading} />
        <WaitlistsDataTable />
      </section>
    </main>
  );
}
