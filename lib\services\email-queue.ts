// Email queuing and batch processing service for performance optimization
// Following utility object pattern instead of class

interface QueuedEmail {
  id: string;
  type: string;
  priority: 'high' | 'medium' | 'low';
  emailData: Record<string, unknown>;
  recipients: string[];
  templateFunction: () => Promise<unknown>;
  scheduledTime?: string;
  retryCount: number;
  maxRetries: number;
  createdAt: string;
  status: 'pending' | 'processing' | 'sent' | 'failed' | 'cancelled';
}

interface BatchConfig {
  maxBatchSize: number;
  batchInterval: number; // milliseconds
  priorityWeights: {
    high: number;
    medium: number;
    low: number;
  };
}

const defaultBatchConfig: BatchConfig = {
  maxBatchSize: 10,
  batchInterval: 5000, // 5 seconds
  priorityWeights: {
    high: 3,
    medium: 2,
    low: 1,
  },
};

// In-memory queue (in production, use Redis or database)
const emailQueue: Map<string, QueuedEmail> = new Map();
const processingQueue: Set<string> = new Set();
let batchProcessor: NodeJS.Timeout | null = null;
let isProcessing = false;

const emailQueueService = {
  // Add email to queue with priority
  enqueueEmail: (
    emailId: string,
    type: string,
    priority: QueuedEmail['priority'],
    emailData: Record<string, unknown>,
    recipients: string[],
    templateFunction: () => Promise<unknown>,
    scheduledTime?: string
  ): Promise<QueuedEmail> => {
    return new Promise((resolve) => {
      const queuedEmail: QueuedEmail = {
        id: emailId,
        type,
        priority,
        emailData,
        recipients,
        templateFunction,
        scheduledTime,
        retryCount: 0,
        maxRetries: 3,
        createdAt: new Date().toISOString(),
        status: 'pending',
      };

      emailQueue.set(emailId, queuedEmail);

      console.log(`Email ${emailId} queued with priority ${priority}`, {
        type,
        recipients: recipients.length,
        scheduledTime,
      });

      // Start batch processor if not already running
      emailQueueService.startBatchProcessor();

      resolve(queuedEmail);
    });
  },

  // Get emails ready for processing (sorted by priority and creation time)
  getEmailsForProcessing: (
    maxCount: number = defaultBatchConfig.maxBatchSize
  ): Promise<QueuedEmail[]> => {
    return new Promise((resolve) => {
      const now = new Date().toISOString();
      const readyEmails: QueuedEmail[] = [];

      for (const email of emailQueue.values()) {
        // Skip if already processing or not ready
        if (processingQueue.has(email.id) || email.status !== 'pending') {
          continue;
        }

        // Check if scheduled time has passed
        if (email.scheduledTime && email.scheduledTime > now) {
          continue;
        }

        readyEmails.push(email);
      }

      // Sort by priority (high to low) then by creation time (oldest first)
      readyEmails.sort((a, b) => {
        const priorityDiff =
          defaultBatchConfig.priorityWeights[b.priority] -
          defaultBatchConfig.priorityWeights[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.createdAt.localeCompare(b.createdAt);
      });

      resolve(readyEmails.slice(0, maxCount));
    });
  },

  // Process a batch of emails
  processBatch: (
    emails: QueuedEmail[]
  ): Promise<{ sent: number; failed: number }> => {
    return new Promise((resolve) => {
      if (emails.length === 0) {
        resolve({ sent: 0, failed: 0 });
        return;
      }

      console.log(`Processing batch of ${emails.length} emails`);

      let sent = 0;
      let failed = 0;
      const promises: Promise<void>[] = [];

      emails.forEach((email) => {
        // Mark as processing
        processingQueue.add(email.id);
        email.status = 'processing';
        emailQueue.set(email.id, email);

        const promise = email
          .templateFunction()
          .then(() => {
            // Success
            email.status = 'sent';
            emailQueue.set(email.id, email);
            processingQueue.delete(email.id);
            sent++;

            console.log(`Email ${email.id} sent successfully`);
          })
          .catch((error) => {
            // Failure
            email.retryCount++;
            email.status =
              email.retryCount >= email.maxRetries ? 'failed' : 'pending';
            emailQueue.set(email.id, email);
            processingQueue.delete(email.id);
            failed++;

            console.error(`Email ${email.id} failed:`, error.message);

            // If retries available, re-queue with lower priority
            if (email.retryCount < email.maxRetries) {
              email.priority = 'low'; // Lower priority for retries
              console.log(
                `Email ${email.id} will be retried (attempt ${email.retryCount + 1}/${email.maxRetries})`
              );
            }
          });

        promises.push(promise);
      });

      Promise.allSettled(promises).then(() => {
        console.log(
          `Batch processing complete: ${sent} sent, ${failed} failed`
        );
        resolve({ sent, failed });
      });
    });
  },

  // Start the batch processor
  startBatchProcessor: (
    config: BatchConfig = defaultBatchConfig
  ): Promise<void> => {
    return new Promise((resolve) => {
      if (batchProcessor) {
        resolve();
        return;
      }

      console.log('Starting email batch processor', {
        maxBatchSize: config.maxBatchSize,
        batchInterval: config.batchInterval,
      });

      batchProcessor = setInterval(() => {
        if (isProcessing) return;

        isProcessing = true;
        emailQueueService
          .getEmailsForProcessing(config.maxBatchSize)
          .then((emails) => {
            if (emails.length > 0) {
              return emailQueueService.processBatch(emails);
            }
            return { sent: 0, failed: 0 };
          })
          .then(({ sent, failed }) => {
            if (sent > 0 || failed > 0) {
              console.log(
                `Batch processor stats: ${sent} sent, ${failed} failed`
              );
            }
          })
          .catch((error) => {
            console.error('Batch processor error:', error);
          })
          .finally(() => {
            isProcessing = false;
          });
      }, config.batchInterval);

      resolve();
    });
  },

  // Stop the batch processor
  stopBatchProcessor: (): Promise<void> => {
    return new Promise((resolve) => {
      if (batchProcessor) {
        clearInterval(batchProcessor);
        batchProcessor = null;
        console.log('Email batch processor stopped');
      }
      resolve();
    });
  },

  // Get queue statistics
  getQueueStats: (): Promise<{
    total: number;
    pending: number;
    processing: number;
    sent: number;
    failed: number;
    byPriority: Record<string, number>;
  }> => {
    return new Promise((resolve) => {
      const stats = {
        total: emailQueue.size,
        pending: 0,
        processing: 0,
        sent: 0,
        failed: 0,
        byPriority: { high: 0, medium: 0, low: 0 },
      };

      for (const email of emailQueue.values()) {
        if (email.status in stats) {
          (stats as unknown as Record<string, number>)[email.status]++;
        }
        stats.byPriority[email.priority]++;
      }

      resolve(stats);
    });
  },

  // Cancel queued email
  cancelEmail: (emailId: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const email = emailQueue.get(emailId);
      if (!email) {
        resolve(false);
        return;
      }

      if (email.status === 'processing') {
        resolve(false); // Cannot cancel processing emails
        return;
      }

      email.status = 'cancelled';
      emailQueue.set(emailId, email);
      processingQueue.delete(emailId);

      console.log(`Email ${emailId} cancelled`);
      resolve(true);
    });
  },

  // Clean up old emails from queue
  cleanupQueue: (maxAge: number = 24 * 60 * 60 * 1000): Promise<number> => {
    return new Promise((resolve) => {
      const cutoffTime = new Date(Date.now() - maxAge).toISOString();
      let cleanedCount = 0;

      for (const [id, email] of emailQueue.entries()) {
        if (
          email.createdAt < cutoffTime &&
          (email.status === 'sent' ||
            email.status === 'failed' ||
            email.status === 'cancelled')
        ) {
          emailQueue.delete(id);
          processingQueue.delete(id);
          cleanedCount++;
        }
      }

      console.log(`Cleaned up ${cleanedCount} old emails from queue`);
      resolve(cleanedCount);
    });
  },

  // Bulk enqueue multiple emails
  bulkEnqueue: (
    emails: Array<{
      id: string;
      type: string;
      priority: QueuedEmail['priority'];
      emailData: Record<string, unknown>;
      recipients: string[];
      templateFunction: () => Promise<unknown>;
      scheduledTime?: string;
    }>
  ): Promise<QueuedEmail[]> => {
    return new Promise((resolve) => {
      const queuedEmails: QueuedEmail[] = [];

      emails.forEach((emailInfo) => {
        const queuedEmail: QueuedEmail = {
          id: emailInfo.id,
          type: emailInfo.type,
          priority: emailInfo.priority,
          emailData: emailInfo.emailData,
          recipients: emailInfo.recipients,
          templateFunction: emailInfo.templateFunction,
          scheduledTime: emailInfo.scheduledTime,
          retryCount: 0,
          maxRetries: 3,
          createdAt: new Date().toISOString(),
          status: 'pending',
        };

        emailQueue.set(emailInfo.id, queuedEmail);
        queuedEmails.push(queuedEmail);
      });

      console.log(`Bulk enqueued ${emails.length} emails`);

      // Start batch processor if not already running
      emailQueueService.startBatchProcessor();

      resolve(queuedEmails);
    });
  },
};

export { emailQueueService };
export type { QueuedEmail, BatchConfig };
