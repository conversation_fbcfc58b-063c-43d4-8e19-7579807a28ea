'use client';

import {
  AlertCircle,
  Banknote,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  Eye,
  Plus,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { useBudgets, useProfile } from '@/hooks/use-db';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/format-currency';

interface PayoutRequest {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'approved' | 'paid' | 'rejected';
  requestDate: string;
  processedDate?: string;
  method: 'bank_transfer' | 'paypal' | 'stripe';
  notes?: string;
  commissionIds: string[];
}

interface PayoutManagementProps {
  className?: string;
}

export function PayoutManagement({ className }: PayoutManagementProps) {
  const { budgets } = useBudgets();
  const { profile } = useProfile();

  const [isRequestDialogOpen, setIsRequestDialogOpen] = useState(false);
  const [requestAmount, setRequestAmount] = useState('');
  const [payoutMethod, setPayoutMethod] = useState<string>('');
  const [requestNotes, setRequestNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock payout requests data (in real app, this would come from database)
  const [payoutRequests] = useState<PayoutRequest[]>([
    {
      id: '1',
      amount: 1250.0,
      currency: 'USD',
      status: 'paid',
      requestDate: '2024-01-15',
      processedDate: '2024-01-18',
      method: 'bank_transfer',
      notes: 'Q4 2023 commissions',
      commissionIds: ['comm1', 'comm2'],
    },
    {
      id: '2',
      amount: 850.0,
      currency: 'USD',
      status: 'pending',
      requestDate: '2024-02-01',
      method: 'paypal',
      notes: 'January 2024 commissions',
      commissionIds: ['comm3'],
    },
  ]);

  // Calculate available commission balance
  const availableCommissions = useMemo(() => {
    if (!profile?.id) return { amount: 0, commissions: [] };

    const earnedCommissions = budgets.filter(
      (budget) =>
        budget.has_affiliate &&
        budget.affiliateId === profile.id &&
        budget.Status === 'spent' // Only completed projects
    );

    const totalEarned = earnedCommissions.reduce(
      (sum, budget) => sum + (budget.AffiliateCommission || 0),
      0
    );

    // Subtract already requested payouts
    const totalRequested = payoutRequests
      .filter((request) => request.status !== 'rejected')
      .reduce((sum, request) => sum + request.amount, 0);

    return {
      amount: totalEarned - totalRequested,
      commissions: earnedCommissions,
      totalEarned,
      totalRequested,
    };
  }, [budgets, profile?.id, payoutRequests]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className='h-4 w-4' />;
      case 'approved':
        return <CheckCircle className='h-4 w-4' />;
      case 'pending':
        return <Clock className='h-4 w-4' />;
      case 'rejected':
        return <AlertCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'bank_transfer':
        return <Building className='h-4 w-4' />;
      case 'paypal':
      case 'stripe':
        return <CreditCard className='h-4 w-4' />;
      default:
        return <Banknote className='h-4 w-4' />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleRequestPayout = async () => {
    const amount = parseFloat(requestAmount);

    if (!amount || amount <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (amount > availableCommissions.amount) {
      toast.error('Amount exceeds available commission balance');
      return;
    }

    if (!payoutMethod) {
      toast.error('Please select a payout method');
      return;
    }

    setIsSubmitting(true);

    try {
      // In real app, this would make an API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast.success('Payout request submitted successfully');
      setIsRequestDialogOpen(false);
      setRequestAmount('');
      setPayoutMethod('');
      setRequestNotes('');
    } catch (error) {
      toast.error('Failed to submit payout request');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Payout Overview */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Available Balance
            </CardTitle>
            <Banknote className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-green-600'>
              {formatCurrency(availableCommissions.amount || 0, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>Ready for payout</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Earned</CardTitle>
            <CheckCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(availableCommissions.totalEarned || 0, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Lifetime commissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Pending Requests
            </CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {payoutRequests.filter((r) => r.status === 'pending').length}
            </div>
            <p className='text-xs text-muted-foreground'>Awaiting processing</p>
          </CardContent>
        </Card>
      </div>

      {/* Payout Requests */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle className='flex items-center gap-2'>
                <Banknote className='h-5 w-5' />
                Payout Requests
              </CardTitle>
              <CardDescription>
                Manage your commission payout requests
              </CardDescription>
            </div>
            <Dialog
              open={isRequestDialogOpen}
              onOpenChange={setIsRequestDialogOpen}
            >
              <DialogTrigger asChild>
                <Button
                  className='flex items-center gap-2'
                  disabled={availableCommissions.amount <= 0}
                >
                  <Plus className='h-4 w-4' />
                  Request Payout
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Request Payout</DialogTitle>
                  <DialogDescription>
                    Request a payout from your available commission balance
                  </DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <div className='p-4 bg-muted rounded-lg'>
                    <div className='text-sm font-medium mb-2'>
                      Available Balance
                    </div>
                    <div className='text-2xl font-bold text-green-600'>
                      {formatCurrency(availableCommissions.amount || 0, 'USD')}
                    </div>
                  </div>

                  <div>
                    <Label>Payout Amount *</Label>
                    <Input
                      type='number'
                      placeholder='0.00'
                      value={requestAmount}
                      onChange={(e) => setRequestAmount(e.target.value)}
                      max={availableCommissions.amount}
                      step='0.01'
                      className='mt-2'
                    />
                  </div>

                  <div>
                    <Label>Payout Method *</Label>
                    <Select
                      value={payoutMethod}
                      onValueChange={setPayoutMethod}
                    >
                      <SelectTrigger className='mt-2'>
                        <SelectValue placeholder='Select payout method' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='bank_transfer'>
                          Bank Transfer
                        </SelectItem>
                        <SelectItem value='paypal'>PayPal</SelectItem>
                        <SelectItem value='stripe'>Stripe</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Notes</Label>
                    <Textarea
                      placeholder='Optional notes about this payout request...'
                      value={requestNotes}
                      onChange={(e) => setRequestNotes(e.target.value)}
                      className='mt-2'
                      rows={3}
                    />
                  </div>

                  <div className='flex gap-3 pt-4'>
                    <Button
                      onClick={handleRequestPayout}
                      disabled={isSubmitting}
                      className='flex-1'
                    >
                      {isSubmitting ? 'Submitting...' : 'Submit Request'}
                    </Button>
                    <Button
                      variant='outline'
                      onClick={() => setIsRequestDialogOpen(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {payoutRequests.length > 0 ? (
            <div className='border rounded-lg'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Request Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Processed Date</TableHead>
                    <TableHead>Notes</TableHead>
                    <TableHead className='w-[80px]'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payoutRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div className='flex items-center gap-2'>
                          <Calendar className='h-4 w-4 text-muted-foreground' />
                          {formatDate(request.requestDate)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='font-medium'>
                          {formatCurrency(request.amount, request.currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center gap-2'>
                          {getMethodIcon(request.method)}
                          <span className='capitalize'>
                            {request.method.replace('_', ' ')}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center gap-2'>
                          {getStatusIcon(request.status)}
                          <Badge className={getStatusColor(request.status)}>
                            {request.status}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        {request.processedDate ? (
                          <div className='flex items-center gap-2'>
                            <Calendar className='h-4 w-4 text-muted-foreground' />
                            {formatDate(request.processedDate)}
                          </div>
                        ) : (
                          <span className='text-muted-foreground'>—</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className='max-w-[200px] truncate'>
                          {request.notes || '—'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          size='sm'
                          variant='ghost'
                          className='h-8 w-8 p-0'
                        >
                          <Eye className='h-4 w-4' />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className='text-center py-8 text-muted-foreground'>
              <Banknote className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>No payout requests yet</p>
              <p className='text-sm'>
                Request your first payout when you have available commissions
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Commission Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Commission Summary</CardTitle>
          <CardDescription>
            Breakdown of your commission earnings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            <div className='flex justify-between items-center'>
              <span>Total Commissions Earned</span>
              <span className='font-medium'>
                {formatCurrency(availableCommissions.totalEarned || 0, 'USD')}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span>Total Requested</span>
              <span className='font-medium'>
                {formatCurrency(
                  availableCommissions.totalRequested || 0,
                  'USD'
                )}
              </span>
            </div>
            <div className='flex justify-between items-center pt-2 border-t'>
              <span className='font-medium'>Available for Payout</span>
              <span className='font-bold text-green-600'>
                {formatCurrency(availableCommissions.amount || 0, 'USD')}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
