'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Clock, Shield, UserCheck, XCircle } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface VolunteerStatusBadgeProps {
  status: 'active' | 'pending' | 'approved' | 'rejected' | 'nda_required' | 'onboarding';
  ndaSigned?: boolean;
  className?: string;
}

export function VolunteerStatusBadge({ 
  status, 
  ndaSigned = false, 
  className 
}: VolunteerStatusBadgeProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'active':
        return {
          label: 'Active Volunteer',
          icon: UserCheck,
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
          description: 'Actively contributing to projects',
        };
      case 'approved':
        return {
          label: 'Approved',
          icon: CheckCircle,
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
          description: 'Application approved, ready to start volunteering',
        };
      case 'pending':
        return {
          label: 'Pending Review',
          icon: Clock,
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
          description: 'Application under review by admin team',
        };
      case 'rejected':
        return {
          label: 'Application Rejected',
          icon: XCircle,
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
          description: 'Application was not approved',
        };
      case 'nda_required':
        return {
          label: 'NDA Required',
          icon: Shield,
          variant: 'outline' as const,
          className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
          description: 'Must sign NDA agreement before accessing projects',
        };
      case 'onboarding':
        return {
          label: 'Onboarding',
          icon: Clock,
          variant: 'secondary' as const,
          className: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
          description: 'Completing onboarding process',
        };
      default:
        return {
          label: 'Unknown Status',
          icon: Clock,
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
          description: 'Status unknown',
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant={config.variant}
            className={`${config.className} ${className} flex items-center gap-1`}
          >
            <Icon className='h-3 w-3' />
            {config.label}
            {status === 'active' && ndaSigned && (
              <Shield className='h-3 w-3 ml-1' />
            )}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{config.description}</p>
          {status === 'active' && ndaSigned && (
            <p className='text-xs text-muted-foreground mt-1'>NDA signed ✓</p>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Volunteer permission indicator
interface VolunteerPermissionBadgeProps {
  permissions: ('view' | 'edit_status' | 'edit_progress' | 'comment' | 'upload')[];
  className?: string;
}

export function VolunteerPermissionBadge({ 
  permissions, 
  className 
}: VolunteerPermissionBadgeProps) {
  const getPermissionLevel = () => {
    if (permissions.includes('edit_progress') && permissions.includes('edit_status')) {
      return {
        label: 'Full Access',
        color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
        description: 'Can edit status, progress, and add comments',
      };
    } else if (permissions.includes('edit_status') || permissions.includes('edit_progress')) {
      return {
        label: 'Limited Edit',
        color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
        description: 'Can edit some fields and add comments',
      };
    } else if (permissions.includes('comment')) {
      return {
        label: 'Comment Only',
        color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        description: 'Can view and add comments only',
      };
    } else {
      return {
        label: 'View Only',
        color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
        description: 'Read-only access',
      };
    }
  };

  const config = getPermissionLevel();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant='outline'
            className={`${config.color} ${className} text-xs`}
          >
            {config.label}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{config.description}</p>
          <div className='mt-2 space-y-1'>
            {permissions.map((permission) => (
              <div key={permission} className='flex items-center gap-2 text-xs'>
                <CheckCircle className='h-3 w-3 text-green-500' />
                <span className='capitalize'>{permission.replace('_', ' ')}</span>
              </div>
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Volunteer contribution level badge
interface ContributionLevelBadgeProps {
  score: number;
  className?: string;
}

export function ContributionLevelBadge({ score, className }: ContributionLevelBadgeProps) {
  const getLevel = () => {
    if (score >= 800) return { 
      level: 'Expert', 
      color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      icon: '🏆'
    };
    if (score >= 600) return { 
      level: 'Advanced', 
      color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      icon: '⭐'
    };
    if (score >= 400) return { 
      level: 'Intermediate', 
      color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      icon: '🌟'
    };
    if (score >= 200) return { 
      level: 'Beginner', 
      color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      icon: '🌱'
    };
    return { 
      level: 'New', 
      color: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
      icon: '👋'
    };
  };

  const config = getLevel();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant='outline'
            className={`${config.color} ${className} flex items-center gap-1`}
          >
            <span>{config.icon}</span>
            {config.level}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>Contribution Score: {score}/1000</p>
          <p className='text-xs text-muted-foreground mt-1'>
            Based on completed issues, hours contributed, and project participation
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
