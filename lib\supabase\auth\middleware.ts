import { createServerClient } from '@supabase/ssr';
import { type NextRequest, NextResponse } from 'next/server';
import type { Database } from './../database-types';

type UserRole = Database['public']['Enums']['Role Types'];

// Comprehensive route definitions based on your documents
const detailedRoutePatterns = {
  admin: [
    '/admin',
    '/admin/dashboard',
    '/admin/database',
    '/admin/dashboard-overview',
    '/admin/dashboard-overview/projects',
    '/admin/dashboard-overview/projects/active',
    '/admin/dashboard-overview/projects/under-review',
    '/admin/dashboard-overview/projects/overdue',
    '/admin/dashboard-overview/projects/list',
    '/admin/dashboard-overview/projects/[projectId]',
    '/admin/dashboard-overview/projects/[projectId]/overview',
    '/admin/dashboard-overview/projects/[projectId]/tasks',
    '/admin/dashboard-overview/projects/[projectId]/info',
    '/admin/dashboard-overview/projects/[projectId]/mark-under-review',
    '/admin/dashboard-overview/projects/[projectId]/mark-completed',
    '/admin/dashboard-overview/projects/[projectId]/assign-affiliate',
    '/admin/dashboard-overview/projects/[projectId]/create-google-space',
    '/admin/dashboard-overview/projects/create',
    '/admin/dashboard-overview/projects/create/new-project',
    '/admin/dashboard-overview/projects/create/fill-details',
    '/admin/dashboard-overview/projects/create/collaboration',
    '/admin/dashboard-overview/projects/create/submit',
    '/admin/dashboard-overview/projects/backlog',
    '/admin/dashboard-overview/projects/completed',
    '/admin/dashboard-overview/tasks',
    '/admin/dashboard-overview/tasks/active',
    '/admin/dashboard-overview/tasks/under-review',
    '/admin/dashboard-overview/tasks/overdue',
    '/admin/dashboard-overview/tasks/list',
    '/admin/dashboard-overview/tasks/[taskId]',
    '/admin/dashboard-overview/tasks/[taskId]/overview',
    '/admin/dashboard-overview/tasks/[taskId]/sub-tasks',
    '/admin/dashboard-overview/tasks/[taskId]/info',
    '/admin/dashboard-overview/tasks/[taskId]/mark-under-review',
    '/admin/dashboard-overview/tasks/[taskId]/mark-completed',
    '/admin/dashboard-overview/tasks/create',
    '/admin/dashboard-overview/tasks/create/new-task',
    '/admin/dashboard-overview/tasks/create/fill-details',
    '/admin/dashboard-overview/tasks/create/submit',
    '/admin/dashboard-overview/tasks/backlog',
    '/admin/dashboard-overview/tasks/completed',
    '/admin/dashboard-overview/performance',
    '/admin/dashboard-overview/performance/best-affiliate',
    '/admin/dashboard-overview/performance/best-collaborator',
    '/admin/dashboard-overview/revenue',
    '/admin/dashboard-overview/revenue/total',
    '/admin/dashboard-overview/revenue/to-payouts-chart',
    '/admin/dashboard-overview/revenue/ready-earnings',
    '/admin/dashboard-overview/revenue/ready-earnings/per-project',
    '/admin/dashboard-overview/revenue/pending-payouts',
    '/admin/dashboard-overview/payouts',
    '/admin/dashboard-overview/payouts/overview',
    '/admin/dashboard-overview/payouts/pending',
    '/admin/dashboard-overview/payouts/completed',
    '/admin/collaborators',
    '/admin/collaborators/list',
    '/admin/collaborators/[collaboratorId]',
    '/admin/collaborators/[collaboratorId]/overview',
    '/admin/collaborators/[collaboratorId]/performance',
    '/admin/collaborators/[collaboratorId]/performance/task-progress',
    '/admin/collaborators/[collaboratorId]/performance/deadlines-met',
    '/admin/collaborators/[collaboratorId]/performance/approval-status',
    '/admin/collaborators/[collaboratorId]/skills',
    '/admin/collaborators/[collaboratorId]/projects',
    '/admin/collaborators/[collaboratorId]/projects/pending',
    '/admin/collaborators/[collaboratorId]/projects/active',
    '/admin/collaborators/[collaboratorId]/projects/under-review',
    '/admin/collaborators/[collaboratorId]/projects/overdue',
    '/admin/collaborators/[collaboratorId]/projects/completed',
    '/admin/collaborators/[collaboratorId]/earnings',
    '/admin/collaborators/[collaboratorId]/earnings/incoming',
    '/admin/collaborators/[collaboratorId]/earnings/ready',
    '/admin/collaborators/[collaboratorId]/earnings/total',
    '/admin/collaborators/[collaboratorId]/earnings/payouts',
    '/admin/collaborators/[collaboratorId]/earnings/payouts/pending',
    '/admin/collaborators/[collaboratorId]/earnings/payouts/completed',
    '/admin/collaborators/[collaboratorId]/assign-to-project',
    '/admin/collaborators/[collaboratorId]/assign-to-project/existing',
    '/admin/collaborators/[collaboratorId]/assign-to-project/new-project',
    '/admin/collaborators/[collaboratorId]/assign-to-project/submit',
    '/admin/collaborators/payouts',
    '/admin/collaborators/payouts/approve',
    '/admin/collaborators/payouts/approve/authentication',
    '/admin/collaborators/payouts/approve/payment',
    '/admin/collaborators/payouts/pending',
    '/admin/collaborators/payouts/completed',
    '/admin/affiliate',
    '/admin/affiliate/list',
    '/admin/affiliate/[affiliateId]',
    '/admin/affiliate/[affiliateId]/overview',
    '/admin/affiliate/[affiliateId]/performance',
    '/admin/affiliate/[affiliateId]/performance/task-progress',
    '/admin/affiliate/[affiliateId]/performance/deadlines-met',
    '/admin/affiliate/[affiliateId]/performance/approval-status',
    '/admin/affiliate/[affiliateId]/skills',
    '/admin/affiliate/[affiliateId]/client-projects',
    '/admin/affiliate/[affiliateId]/client-projects/pending',
    '/admin/affiliate/[affiliateId]/client-projects/active',
    '/admin/affiliate/[affiliateId]/client-projects/under-review',
    '/admin/affiliate/[affiliateId]/client-projects/rejected',
    '/admin/affiliate/[affiliateId]/client-projects/completed',
    '/admin/affiliate/[affiliateId]/earnings',
    '/admin/affiliate/[affiliateId]/earnings/incoming',
    '/admin/affiliate/[affiliateId]/earnings/ready',
    '/admin/affiliate/[affiliateId]/earnings/total',
    '/admin/affiliate/[affiliateId]/earnings/payouts',
    '/admin/affiliate/[affiliateId]/earnings/payouts/pending',
    '/admin/affiliate/[affiliateId]/earnings/payouts/completed',
    '/admin/affiliate/[affiliateId]/assign-to-project',
    '/admin/affiliate/applications',
    '/admin/affiliate/applications/new',
    '/admin/affiliate/applications/new/review',
    '/admin/affiliate/applications/new/accept',
    '/admin/affiliate/applications/new/send-rejection-email',
    '/admin/affiliate/applications/new/send-acceptance-email',
    '/admin/affiliate/applications/new/enroll',
    '/admin/affiliate/applications/pending',
    '/admin/affiliate/applications/rejected',
    '/admin/affiliate/applications/enrolled',
    '/admin/affiliate/client-requests',
    '/admin/affiliate/client-requests/review',
    '/admin/affiliate/client-requests/review/google-form',
    '/admin/affiliate/client-requests/review/create-brief',
    '/admin/affiliate/client-requests/review/send-brief',
    '/admin/affiliate/client-requests/review/revision',
    '/admin/affiliate/client-requests/review/approve-brief',
    '/admin/affiliate/client-requests/review/send-rejection-email',
    '/admin/affiliate/client-requests/review/send-acceptance-email',
    '/admin/affiliate/client-requests/review/receive-retainer-fee',
    '/admin/affiliate/client-requests/review/mark-pending',
    '/admin/affiliate/client-requests/review/create-project',
    '/admin/affiliate/client-requests/quote',
    '/admin/affiliate/client-requests/quote/receive',
    '/admin/affiliate/client-requests/quote/review',
    '/admin/affiliate/client-requests/quote/accept',
    '/admin/affiliate/client-requests/quote/negotiate',
    '/admin/affiliate/client-requests/quote/send-rejection-email',
    '/admin/affiliate/client-requests/quote/send-acceptance-email',
    '/admin/affiliate/client-requests/quote/send-dashboard-logins',
    '/admin/affiliate/client-requests/quote/enroll',
    '/admin/affiliate/client-requests/payouts',
    '/admin/affiliate/client-requests/payouts/approve',
    '/admin/affiliate/client-requests/payouts/approve/authentication',
    '/admin/affiliate/client-requests/payouts/approve/payment',
    '/admin/affiliate/client-requests/payouts/pending',
    '/admin/affiliate/client-requests/payouts/completed',
  ],
  affiliate: [
    '/affiliate',
    '/affiliate/application',
    '/affiliate/application/submit',
    '/affiliate/application/acknowledge',
    '/affiliate/dashboard',
    '/affiliate/dashboard/login',
    '/affiliate/dashboard/nda',
    '/affiliate/dashboard/nda/sign',
    '/affiliate/dashboard-overview',
    '/affiliate/dashboard/profile',
    '/affiliate/dashboard/profile/edit',
    '/affiliate/dashboard/profile/portfolio',
    '/affiliate/dashboard/settings',
    '/affiliate/referrals',
    '/affiliate/referrals/overview',
    '/affiliate/referrals/list',
    '/affiliate/referrals/list/rejected',
    '/affiliate/referrals/list/under-review',
    '/affiliate/referrals/list/approved',
    '/affiliate/referrals/list/active',
    '/affiliate/referrals/list/completed',
    '/affiliate/referrals/new',
    '/affiliate/referrals/new/review',
    '/affiliate/referrals/new/google-form',
    '/affiliate/referrals/new/create-brief',
    '/affiliate/referrals/new/send-brief',
    '/affiliate/referrals/new/revision',
    '/affiliate/referrals/new/approve',
    '/affiliate/referrals/new/approve/accept',
    '/affiliate/referrals/new/approve/reject',
    '/affiliate/referrals/new/retainer-fee',
    '/affiliate/referrals/new/start-work',
    '/affiliate/referrals/new/progress',
    '/affiliate/referrals/new/complete',
    '/affiliate/earnings',
    '/affiliate/earnings/overview',
    '/affiliate/earnings/list',
    '/affiliate/earnings/payouts',
    '/affiliate/earnings/payouts/request',
    '/affiliate/earnings/payouts/amount',
    '/affiliate/earnings/payouts/bank-info',
    '/affiliate/earnings/payouts/bank-info/edit',
    '/affiliate/earnings/payouts/bank-info/confirm',
    '/affiliate/earnings/payouts/pending',
    '/affiliate/earnings/payouts/completed',
    '/affiliate/earnings/authentication',
  ],
  collaborator: [
    '/collaborator',
    '/collaborator/application',
    '/collaborator/application/submit',
    '/collaborator/application/quote',
    '/collaborator/application/quote/submit',
    '/collaborator/application/quote/negotiate',
    '/collaborator/dashboard',
    '/collaborator/dashboard/login',
    '/collaborator/dashboard/nda',
    '/collaborator/dashboard/nda/sign',
    '/collaborator/dashboard-overview',
    '/collaborator/dashboard/profile',
    '/collaborator/dashboard/profile/edit',
    '/collaborator/dashboard/profile/portfolio',
    '/collaborator/dashboard/settings',
    '/collaborator/projects',
    '/collaborator/projects/overview',
    '/collaborator/projects/list',
    '/collaborator/projects/[projectId]',
    '/collaborator/projects/[projectId]/overview',
    '/collaborator/projects/[projectId]/collaboration',
    '/collaborator/projects/[projectId]/collaboration/google-space',
    '/collaborator/projects/[projectId]/collaboration/progress',
    '/collaborator/projects/[projectId]/collaboration/completion',
    '/collaborator/projects/[projectId]/tasks',
    '/collaborator/projects/[projectId]/tasks/confirm',
    '/collaborator/projects/[projectId]/tasks/submit',
    '/collaborator/projects/[projectId]/tasks/revisions',
    '/collaborator/projects/[projectId]/tasks/final-submit',
    '/collaborator/projects/[projectId]/assets',
    '/collaborator/projects/[projectId]/assets/request',
    '/collaborator/projects/[projectId]/assets/manage',
    '/collaborator/projects/[projectId]/offboarding',
    '/collaborator/earnings',
    '/collaborator/earnings-overview',
    '/collaborator/earnings/list',
    '/collaborator/earnings/payouts',
    '/collaborator/earnings/payouts/request',
    '/collaborator/earnings/payouts/amount',
    '/collaborator/earnings/payouts/bank-info',
    '/collaborator/earnings/payouts/bank-info/edit',
    '/collaborator/earnings/payouts/bank-info/confirm',
    '/collaborator/earnings/payouts/pending',
    '/collaborator/earnings/payouts/completed',
    '/collaborator/earnings/authentication',
  ],
  volunteer: [
    '/volunteer',
    '/volunteer/inbox',
    '/volunteer/projects',
    '/volunteer/projects/[id]',
    '/volunteer/issues',
    '/volunteer/issues/[id]',
    '/volunteer/tasks',
    '/volunteer/portfolio',
    '/volunteer/application',
    '/volunteer/nda',
    '/volunteer/brief',
  ],
};

// Routes that don't require authentication
export const unprotectedRoutes = [
  '/', // Home page
  '/login', // Login page
  '/auth', // Auth callback routes
  '/shared', // Shared document pages
  '/docs', // Documentation
  '/icons', // Documentation
  '/pricing', // Pricing page
  '/about', // About page
  '/contact', // Contact page
  '/waitlist', // Waitlist page
  '/api', // API routes
  '/terms', // Terms page
  '/privacy', // Privacy page
  '/not-found', // 404 page
  '/unauthorized', // Unauthorized page
  '/auth-error', // Auth Error page
];

/**
 * Check if a user has access to a specific route based on their role
 */
function hasRoleAccess(pathname: string, userRole: UserRole): boolean {
  const allowedRoutes =
    detailedRoutePatterns[
      userRole.toLowerCase() as keyof typeof detailedRoutePatterns
    ] || [];

  return allowedRoutes.some((route) => {
    // Handle dynamic routes with brackets [id]
    const routePattern = route
      .replace(/\[projectId\]/g, '[^/]+')
      .replace(/\[taskId\]/g, '[^/]+')
      .replace(/\[collaboratorId\]/g, '[^/]+')
      .replace(/\[affiliateId\]/g, '[^/]+')
      .replace(/\[id\]/g, '[^/]+');

    const regex = new RegExp(`^${routePattern}(/.*)?$`);
    return regex.test(pathname);
  });
}

/**
 * Get the redirect URL based on user role
 */
function getRoleRedirectUrl(role: UserRole): string {
  switch (role.toLowerCase()) {
    case 'admin':
      return '/admin';
    case 'affiliate':
      return '/affiliate';
    case 'collaborator':
      return '/collaborator';
    case 'volunteer':
      return '/volunteer';
    default:
      return '/dashboard';
  }
}

/**
 * Check if a route is a protected route (requires authentication)
 */
function isProtectedRoute(pathname: string): boolean {
  const allProtectedRoutes = [
    ...detailedRoutePatterns.admin,
    ...detailedRoutePatterns.affiliate,
    ...detailedRoutePatterns.collaborator,
    ...detailedRoutePatterns.volunteer,
  ];

  return allProtectedRoutes.some((route) => {
    const routePattern = route
      .replace(/\[projectId\]/g, '[^/]+')
      .replace(/\[taskId\]/g, '[^/]+')
      .replace(/\[collaboratorId\]/g, '[^/]+')
      .replace(/\[affiliateId\]/g, '[^/]+')
      .replace(/\[id\]/g, '[^/]+');

    const regex = new RegExp(`^${routePattern}(/.*)?$`);
    return regex.test(pathname);
  });
}

/**
 * Check if a route is unprotected (no authentication required)
 */
function isUnprotectedRoute(pathname: string): boolean {
  return unprotectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );
}

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const pathname = request.nextUrl.pathname;

  // Check for undefined routes
  if (pathname.includes('/undefined')) {
    const url = request.nextUrl.clone();
    if (!user) {
      url.pathname = '/login';
    } else {
      url.pathname = '/not-found-handler';
    }
    return NextResponse.redirect(url);
  }

  // Check if this is an unprotected route
  if (isUnprotectedRoute(pathname)) {
    // If user is logged in and trying to access login or create-account pages,
    // redirect them to their role-specific dashboard
    if (
      user &&
      (pathname === '/login' || pathname === '/create-account') &&
      !pathname.startsWith('/auth/')
    ) {
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (userProfile?.role) {
        const url = request.nextUrl.clone();
        url.pathname = getRoleRedirectUrl(userProfile.role);
        return NextResponse.redirect(url);
      }
    }
    return supabaseResponse;
  }

  // Check if this is a top-level route (like /something)
  const isTopLevelRoute = /^\/[^/]+$/.test(pathname);

  // Handle username routes (top-level routes that might be usernames)
  if (
    isTopLevelRoute &&
    !isUnprotectedRoute(pathname) &&
    !isProtectedRoute(pathname)
  ) {
    if (user) {
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Check if this path matches the logged-in user's username
      if (pathname === `/${userProfile?.username}`) {
        // Redirect to role-specific dashboard
        const url = request.nextUrl.clone();
        url.pathname = getRoleRedirectUrl(userProfile?.role!);
        return NextResponse.redirect(url);
      } else {
        // Check if this path could be any valid username
        const username = pathname.substring(1);
        const { data: matchedProfile } = await supabase
          .from('profiles')
          .select('username')
          .eq('username', username)
          .single();

        if (matchedProfile) {
          // Valid username but not current user - redirect to unauthorized
          const url = request.nextUrl.clone();
          url.pathname = '/unauthorized';
          return NextResponse.redirect(url);
        } else {
          // Not a valid username - show 404
          return NextResponse.rewrite(
            new URL('/not-found-handler', request.url)
          );
        }
      }
    } else {
      // No user logged in, check if it's a valid username
      const username = pathname.substring(1);
      const { data: matchedProfile } = await supabase
        .from('profiles')
        .select('username')
        .eq('username', username)
        .single();

      if (matchedProfile) {
        // Valid username but no auth - redirect to login
        const url = request.nextUrl.clone();
        url.pathname = '/login';
        return NextResponse.redirect(url);
      } else {
        // Not a valid username - show 404
        return NextResponse.rewrite(new URL('/not-found-handler', request.url));
      }
    }
  }

  // Handle protected routes
  if (isProtectedRoute(pathname)) {
    if (!user) {
      // Redirect to login for protected routes when not logged in
      console.log(`Redirecting to login from protected route: ${pathname}`);
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }

    // Get user profile for role-based access control
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (!userProfile) {
      // If profile doesn't exist, redirect to login
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }

    // Check role-based access
    if (!hasRoleAccess(pathname, userProfile.role)) {
      console.log(
        `Access denied for role ${userProfile.role} to route: ${pathname}`
      );
      const url = request.nextUrl.clone();
      url.pathname = '/unauthorized';
      return NextResponse.redirect(url);
    }

    console.log(
      `Access granted for role ${userProfile.role} to route: ${pathname}`
    );
    return supabaseResponse;
  }

  // Handle username-based routes for authenticated users
  if (user) {
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (!userProfile) {
      const url = request.nextUrl.clone();
      url.pathname = '/login';
      return NextResponse.redirect(url);
    }

    // Check if this is a username route
    const segments = pathname.split('/').filter(Boolean);
    if (segments.length > 0) {
      const routeUsername = segments[0];

      // If trying to access another user's routes
      if (routeUsername !== userProfile.username) {
        const { data: otherProfile } = await supabase
          .from('profiles')
          .select('username')
          .eq('username', routeUsername)
          .single();

        if (otherProfile) {
          // Username exists but it's not the current user
          const url = request.nextUrl.clone();
          url.pathname = '/unauthorized';
          return NextResponse.redirect(url);
        } else {
          // Username doesn't exist
          return NextResponse.rewrite(
            new URL('/not-found-handler', request.url)
          );
        }
      }
    }
  }

  // If we reach here and it's not an unprotected route, show 404
  if (!isUnprotectedRoute(pathname)) {
    console.log(`Showing not-found for unknown route: ${pathname}`);
    return NextResponse.rewrite(new URL('/not-found-handler', request.url));
  }

  return supabaseResponse;
}
