'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { useIssues, usePortfolio } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';
import { columns } from './columns';

interface CollaboratorIssuesTableProps {
  issues?: Issue[];
  loading?: boolean;
}

export function CollaboratorIssuesTable({
  issues: externalIssues,
  loading: externalLoading,
}: CollaboratorIssuesTableProps = {}) {
  const { getCollaboratorIssues } = useIssues();
  const { portfolioData } = usePortfolio();
  const [internalIssues, setInternalIssues] = useState<Issue[]>([]);
  const [internalLoading, setInternalLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use external data if provided, otherwise fetch internally
  const issues = externalIssues !== undefined ? externalIssues : internalIssues;
  const loading =
    externalLoading !== undefined ? externalLoading : internalLoading;

  useEffect(() => {
    // Don't fetch if external data is provided or if no user
    if (externalIssues !== undefined || !portfolioData?.id) return;

    setInternalLoading(true);
    setError(null);

    getCollaboratorIssues(portfolioData.id)
      .then((collaboratorIssues) => {
        setInternalIssues(collaboratorIssues);
      })
      .catch((error) => {
        console.error('Error fetching collaborator issues:', error);
        setError('Failed to load issues');
        toast.error('Failed to load issues');
      })
      .finally(() => {
        setInternalLoading(false);
      });
  }, [portfolioData?.id, getCollaboratorIssues, externalIssues]);

  if (error) {
    return (
      <div className='flex items-center justify-center h-32'>
        <p className='text-muted-foreground'>{error}</p>
      </div>
    );
  }

  return <DataTable columns={columns} data={issues} isLoading={loading} />;
}
