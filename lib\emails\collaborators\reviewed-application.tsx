import type { Database } from '@/lib/supabase/database-types';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import * as React from 'react';
import { z } from 'zod';

const baseUrl = 'https://www.thehuefactory.co/';

type dataProps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
  '50': '#f8e1db',
};

export const ReviewedApplication = ({ full_name, join_role }: dataProps) => (
  <Html>
    <Head />
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['50'],
        }}
      >
        <Img
          src={`${baseUrl}/email_hero.jpg`}
          width='100%'
          height='auto'
          alt='Email Header Image'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['50'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        ></Section>
      </Container>
      <Container style={container}>
        <Heading style={h1}>Hey {full_name}, </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Your application for {join_role} has been reviewed and we are excited
          to explore potential opportunities together..
        </Text>
        <Text style={{ ...text, marginBottom: '48px' }}>
          Kindly view our Collaborators' Brief containing our approach and other
          details concerning our collaboration process
        </Text>
        <Link
          href='https://www.thehuefactory.co/assets/briefs/collaborators.pdf'
          target='_blank'
          style={{
            backgroundColor: colors[100],
            paddingRight: 18,
            paddingLeft: 18,
            fontWeight: 'bold',
            color: 'white',
            paddingTop: 16,
            paddingBottom: 16,
            borderRadius: 32,
            whiteSpace: 'nowrap',
            fontFamily: 'monospace',
          }}
        >
          Collaborators' Brief.
        </Link>
        <Text style={{ ...text, marginBottom: '24px', marginTop: '48px' }}>
          To move forward, we kindly request that you reply this mail with your
          your detailed price quotes; containing all your services and their
          respective costs. This will help us align expectations and ensure
          smooth collaboration as we embark on creative projects together.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Kindly note that all quotes will be open to negotiation, and we look
          forward to discussing any adjustments to ensure a fair and productive
          collaboration.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Our team will reach out to you via whatsapp to finalize the
          application process and other legal documents right after your quote
          has been successfully reviewed!
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          You can reply this message with any extra questions or{' '}
          <Link
            href='https://www.thehuefactory.co/contact'
            target='_blank'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
          >
            contact us
          </Link>{' '}
          with your questions.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          We look forward to welcoming you to our team!
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
          height='42'
          alt="thehuefactory's Logo"
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            target='_blank'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2024 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const code = {
  display: 'inline-block',
  padding: '16px 4.5%',
  width: '90.5%',
  backgroundColor: '#f4f4f4',
  borderRadius: '5px',
  border: '1px solid #eee',
  color: '#333',
};
