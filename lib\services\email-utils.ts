// Note: This utility is designed for client-side use and uses API endpoints
// Direct Resend calls are handled server-side in API routes
import { supabaseClient } from '@/lib/supabase/auth/client';

// Import types
import type { Application } from '@/lib/supabase/database-modules';

interface EmailResult {
  success: boolean;
  data?: unknown;
  error?: unknown;
}

// Application email types
type ApplicationEmailType = 'reviewed' | 'approved' | 'notAccepted';
type ApplicationRole = 'Affiliate' | 'Collaborator' | 'Volunteer';

interface ApplicationEmailData extends Application {
  pass?: string; // Password for approved applications
}

// Email utility service using object pattern instead of class - CLIENT-SIDE VERSION
const emailUtils = {
  // Get project members with their email addresses using Promise chains
  getProjectMembers: (projectId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('project_members')
        .select(
          `
          profiles!inner(
            email,
            full_name,
            role
          )
        `
        )
        .eq('project_id', projectId)
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching project members:', error);
            reject(error);
          } else {
            const recipients =
              data
                ?.map(
                  (member: Record<string, unknown>) =>
                    (member.profiles as { email?: string })?.email
                )
                .filter(Boolean) || [];
            resolve(recipients as string[]);
          }
        });
    });
  },

  // Get team members with their email addresses using Promise chains
  getTeamMembers: (teamId: string): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('team_members')
        .select(
          `
          profiles!inner(
            email,
            full_name,
            role
          )
        `
        )
        .eq('team_id', teamId)
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching team members:', error);
            reject(error);
          } else {
            const recipients =
              data?.map((member) => member.profiles?.email).filter(Boolean) ||
              [];
            resolve(recipients as string[]);
          }
        });
    });
  },

  // Get admin email addresses for notifications using Promise chains
  getAdminEmails: (): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('profiles')
        .select('email')
        .eq('role', 'Admin')
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching admin emails:', error);
            reject(error);
          } else {
            const adminEmails =
              data?.map((admin) => admin.email).filter(Boolean) || [];
            // Always include default admin emails as fallback
            const defaultAdmins = ['<EMAIL>'];
            const allAdmins = [...new Set([...adminEmails, ...defaultAdmins])];
            resolve(allAdmins as string[]);
          }
        });
    });
  },

  // Get user details by ID using Promise chains
  getUserDetails: (
    userId: string
  ): Promise<{
    id: string;
    email?: string;
    full_name?: string | null;
    role?: string;
  }> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('profiles')
        .select('id, full_name, email, avatar_url, role')
        .eq('id', userId)
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching user details:', error);
            reject(error);
          } else {
            resolve(data);
          }
        });
    });
  },

  // Get project details by ID using Promise chains
  getProjectDetails: (projectId: string): Promise<Record<string, unknown>> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('projects')
        .select(
          `
          *,
          lead:profiles!lead_id(
            id,
            full_name,
            email,
            avatar_url
          ),
          status:status!status_id(
            id,
            name,
            color,
            icon_name
          ),
          priority:priorities!priority_id(
            id,
            name,
            icon_name,
            sort_order
          )
        `
        )
        .eq('id', projectId)
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching project details:', error);
            reject(error);
          } else {
            resolve(data);
          }
        });
    });
  },

  // Get issue details by ID using Promise chains
  getIssueDetails: (issueId: string): Promise<Record<string, unknown>> => {
    return new Promise((resolve, reject) => {
      supabaseClient
        .from('issues')
        .select(
          `
          *,
          assignee:profiles!assignee_id(
            id,
            full_name,
            email,
            avatar_url
          ),
          status:status!status_id(
            id,
            name,
            color,
            icon_name
          ),
          priority:priorities!priority_id(
            id,
            name,
            icon_name,
            sort_order
          ),
          project:projects!project_id(
            id,
            name,
            description
          )
        `
        )
        .eq('id', issueId)
        .single()
        .then(({ data, error }) => {
          if (error) {
            console.error('Error fetching issue details:', error);
            reject(error);
          } else {
            resolve(data);
          }
        });
    });
  },

  // Note: Template generation is handled server-side in API routes
  // This client-side utility only calls the API endpoints

  // Send application workflow email using Promise chains - CLIENT-SIDE SAFE VERSION
  sendApplicationEmail: (
    role: ApplicationRole,
    emailType: ApplicationEmailType,
    applicationData: ApplicationEmailData
  ): Promise<EmailResult> => {
    return new Promise((resolve, reject) => {
      console.log(
        `🚀 EMAIL UTILITY: Starting application email send process (using API endpoint):`,
        {
          role,
          emailType,
          applicationId: applicationData.id,
          email: applicationData.email,
          fullName: applicationData.full_name,
        }
      );

      // Always use API endpoint for client-side calls to avoid CORS issues
      emailUtils
        .sendApplicationEmailViaAPI(role, emailType, applicationData)
        .then((result) => {
          console.log(`✅ Application email sent successfully via API:`, {
            role,
            emailType,
            applicationId: applicationData.id,
            result,
          });
          resolve(result);
        })
        .catch((error) => {
          console.error(`❌ Failed to send application email via API:`, {
            role,
            emailType,
            applicationId: applicationData.id,
            error,
          });
          reject(error);
        });
    });
  },

  // Send application workflow email via API endpoint (fallback method) using Promise chains
  sendApplicationEmailViaAPI: (
    role: ApplicationRole,
    emailType: ApplicationEmailType,
    applicationData: ApplicationEmailData
  ): Promise<EmailResult> => {
    return new Promise((resolve, reject) => {
      console.log(`Sending application email via API:`, {
        role,
        emailType,
        applicationId: applicationData.id,
        email: applicationData.email,
      });

      const endpoint = `/api/${role.toLowerCase()}/${emailType === 'approved' ? 'approved' : emailType === 'reviewed' ? 'reviewed' : 'not-accepted'}`;

      fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(applicationData),
      })
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then((result) => {
          console.log(`Application email sent via API successfully:`, {
            role,
            emailType,
            applicationId: applicationData.id,
            result,
          });
          resolve({ success: true, data: result });
        })
        .catch((error) => {
          console.error(`Failed to send application email via API:`, {
            role,
            emailType,
            applicationId: applicationData.id,
            error,
          });
          reject({ success: false, error });
        });
    });
  },
};

export { emailUtils };
export type {
  EmailResult,
  ApplicationEmailType,
  ApplicationRole,
  ApplicationEmailData,
};
