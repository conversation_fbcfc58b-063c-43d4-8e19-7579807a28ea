'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Eye, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';

import { StatusIcon } from '@/components/icons/status-icon';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Project } from '@/lib/supabase/database-modules';

export const columns: ColumnDef<Project>[] = [
  {
    accessorKey: 'name',
    header: 'Project Name',
    cell: ({ row }) => {
      const project = row.original;
      return (
        <div className='flex items-center gap-2'>
          <div className='flex flex-col items-start overflow-hidden'>
            <Link
              href={`/volunteer/projects/${project.id}`}
              className='font-medium truncate w-full hover:underline'
            >
              {project.name}
            </Link>
            {project.description && (
              <p className='text-xs text-muted-foreground truncate w-full'>
                {project.description}
              </p>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const project = row.original;
      const status = project.status;

      if (!status) {
        return <span className='text-muted-foreground'>No status</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <StatusIcon status={status.name} />
          <span className='text-sm font-medium'>{status.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      const project = row.original;
      const priority = project.priority;

      if (!priority) {
        return <span className='text-muted-foreground'>No priority set</span>;
      }

      const getPriorityColor = (priorityName: string) => {
        switch (priorityName.toLowerCase()) {
          case 'urgent':
          case 'high':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
          case 'medium':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
          case 'low':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
          default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
      };

      return (
        <Badge className={getPriorityColor(priority.name)}>
          {priority.name}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'lead',
    header: 'Project Lead',
    cell: ({ row }) => {
      const project = row.original;
      const lead = project.lead;

      if (!lead) {
        return <span className='text-muted-foreground'>No lead assigned</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <Avatar className='size-6'>
            <AvatarImage
              src={lead.avatar_url || undefined}
              alt={lead.full_name || lead.email}
            />
            <AvatarFallback>
              {lead.full_name?.split(' ').map(n => n[0]).join('') || lead.email[0]}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm font-medium'>{lead.full_name || lead.email}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'client',
    header: 'Client',
    cell: ({ row }) => {
      const project = row.original;
      const client = project.client;

      if (!client) {
        return <span className='text-muted-foreground'>No client</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <span className='text-sm font-medium'>{client.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ row }) => {
      const project = row.original;
      const dueDate = project.due_date;

      if (!dueDate) {
        return <span className='text-muted-foreground'>No due date</span>;
      }

      const date = new Date(dueDate);
      const now = new Date();
      const isOverdue = date < now;
      const isUpcoming = date.getTime() - now.getTime() < 7 * 24 * 60 * 60 * 1000; // 7 days

      return (
        <div className='flex items-center gap-2'>
          <span 
            className={`text-sm ${
              isOverdue 
                ? 'text-red-600 font-medium' 
                : isUpcoming 
                ? 'text-yellow-600 font-medium' 
                : 'text-muted-foreground'
            }`}
          >
            {date.toLocaleDateString()}
          </span>
          {isOverdue && (
            <Badge variant='destructive' className='text-xs'>
              Overdue
            </Badge>
          )}
          {isUpcoming && !isOverdue && (
            <Badge variant='outline' className='text-xs text-yellow-600 border-yellow-600'>
              Due Soon
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const project = row.original;
      const createdDate = new Date(project.created_at);

      return (
        <span className='text-sm text-muted-foreground'>
          {createdDate.toLocaleDateString()}
        </span>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const project = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='w-64'>
            <DropdownMenuItem asChild>
              <Link href={`/volunteer/projects/${project.id}`}>
                <Eye className='mr-2 h-4 w-4' />
                View Project
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
