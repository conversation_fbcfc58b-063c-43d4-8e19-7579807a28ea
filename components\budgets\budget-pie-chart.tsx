'use client';

import { useMemo } from 'react';
import { Cell, Legend, Pie, Pie<PERSON><PERSON>, ResponsiveContainer } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { Budget, Project } from '@/lib/supabase/database-modules';
import {
  type BudgetBreakdown,
  type BudgetChartData,
  budgetBreakdownToChartData,
  calculateBudgetBreakdown,
  formatCurrency,
  getBudgetHealthStatus,
} from '@/lib/utils/budget-calculations';

interface BudgetPieChartProps {
  budget: Budget | null;
  project: Project | null;
  className?: string;
}

export function BudgetPieChart({
  budget,
  project,
  className,
}: BudgetPieChartProps) {
  const breakdown = useMemo(() => {
    return calculateBudgetBreakdown(budget, project);
  }, [budget, project]);

  const chartData = useMemo(() => {
    return budgetBreakdownToChartData(breakdown);
  }, [breakdown]);

  const healthStatus = useMemo(() => {
    return budget ? getBudgetHealthStatus(budget, breakdown) : 'healthy';
  }, [budget, breakdown]);

  // Chart configuration for shadcn/ui charts
  const chartConfig = useMemo(() => {
    const config: Record<string, { label: string; color: string }> = {};

    chartData.forEach((item) => {
      const key = item.name.toLowerCase().replace(/\s+/g, '_');
      config[key] = {
        label: item.name,
        color: item.color,
      };
    });

    return config;
  }, [chartData]);

  if (!budget || chartData.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Budget Breakdown</CardTitle>
          <CardDescription>No budget data available</CardDescription>
        </CardHeader>
        <CardContent className='flex items-center justify-center h-[300px]'>
          <div className='text-center text-muted-foreground'>
            <p>No budget information found for this project.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currency = budget.Currency || 'USD';
  const totalBudget = breakdown.totalBudget;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className='flex items-center justify-between'>
          Budget Breakdown
          <div
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              healthStatus === 'healthy'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : healthStatus === 'warning'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            }`}
          >
            {healthStatus === 'healthy'
              ? 'Healthy'
              : healthStatus === 'warning'
                ? 'Warning'
                : 'Critical'}
          </div>
        </CardTitle>
        <CardDescription>
          Total Budget: {formatCurrency(totalBudget, currency)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className='h-[280px] sm:h-[320px] w-full'
        >
          <ResponsiveContainer width='100%' height='100%'>
            <PieChart>
              <Pie
                data={chartData}
                cx='50%'
                cy='50%'
                innerRadius={40}
                outerRadius={90}
                paddingAngle={2}
                dataKey='value'
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    formatter={(value: any, name: any) => [
                      formatCurrency(Number(value), currency),
                      String(name),
                    ]}
                  />
                }
              />
              <Legend
                content={({ payload }) => (
                  <div className='flex flex-wrap justify-center gap-4 mt-4'>
                    {payload?.map((entry, index) => (
                      <div key={index} className='flex items-center gap-2'>
                        <div
                          className='w-3 h-3 rounded-full'
                          style={{ backgroundColor: entry.color }}
                        />
                        <span className='text-sm text-muted-foreground'>
                          {entry.value}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Budget Details */}
        <div className='mt-6 space-y-3'>
          {chartData.map((item, index) => (
            <div
              key={index}
              className='flex flex-col sm:flex-row sm:items-center justify-between p-3 bg-muted/50 rounded-lg gap-2'
            >
              <div className='flex items-center gap-3'>
                <div
                  className='w-4 h-4 rounded-full'
                  style={{ backgroundColor: item.color }}
                />
                <span className='font-medium'>{item.name}</span>
              </div>
              <div className='text-right'>
                <div className='font-semibold'>
                  {formatCurrency(item.value, currency)}
                </div>
                <div className='text-sm text-muted-foreground'>
                  {item.percentage}%
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Budget Info */}
        <div className='mt-6 pt-4 border-t border-border'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm'>
            <div>
              <span className='text-muted-foreground'>Status:</span>
              <span className='ml-2 font-medium capitalize'>
                {budget.Status}
              </span>
            </div>
            <div>
              <span className='text-muted-foreground'>Category:</span>
              <span className='ml-2 font-medium capitalize'>
                {budget.Category}
              </span>
            </div>
            <div>
              <span className='text-muted-foreground'>Current Amount:</span>
              <span className='ml-2 font-medium'>
                {formatCurrency(budget.CurrentAmount, currency)}
              </span>
            </div>
            <div>
              <span className='text-muted-foreground'>Spent:</span>
              <span className='ml-2 font-medium'>
                {formatCurrency(
                  budget.ActualAmount - budget.CurrentAmount,
                  currency
                )}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Export types for use in other components
export type { BudgetBreakdown, BudgetChartData };
