<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2a409c52-5d76-437a-a25c-6cd3ca9b49f8" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/admin/issues/[id]/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/collaborator/issues/[id]/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/application/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/brief/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/inbox/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/issues/[id]/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/issues/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/nda/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/portfolio/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/projects/[id]/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/projects/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/tasks/page.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/issues/issue-attachments.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/issues/issue-comments.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/issues/issue-header.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/layouts/sidebar/volunteers/content.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/tables/volunteer-issues/columns.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/tables/volunteer-projects/columns.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/tables/volunteer-tasks/columns.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/volunteer/volunteer-application-form.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/volunteer/volunteer-dashboard.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/volunteer/volunteer-metrics.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/components/volunteer/volunteer-status-badge.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/(dashboard)/volunteer/page.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/(dashboard)/volunteer/page.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/layouts/sidebar/app-sidebar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/layouts/sidebar/app-sidebar.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/tables/collaborator-issues/columns.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/tables/collaborator-issues/columns.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/hooks/use-db.ts" beforeDir="false" afterPath="$PROJECT_DIR$/hooks/use-db.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/supabase/auth/middleware.ts" beforeDir="false" afterPath="$PROJECT_DIR$/lib/supabase/auth/middleware.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="30KPmFzcKDGLFPZqvnbDQCFHKCx" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "ignore.virus.scanning.warn.message": "true",
    "js.debugger.nextJs.config.created.client": "true",
    "js.debugger.nextJs.config.created.server": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "C:/Users/<USER>/dev/startups/team",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "bun",
    "to.speed.mode.migration.done": "true",
    "ts.external.directory.path": "C:\\Users\\<USER>\\dev\\startups\\team\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="npm.Next.js: server-side">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2a409c52-5d76-437a-a25c-6cd3ca9b49f8" name="Changes" comment="" />
      <created>1753373447726</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753373447726</updated>
      <workItem from="1753373449159" duration="62000" />
      <workItem from="1754462747468" duration="365000" />
      <workItem from="1754510028729" duration="4869000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>