import type { Budget, Project } from '@/lib/supabase/database-modules';

export interface BudgetBreakdown {
  affiliateCommission: number;
  miscellaneousCosts: number;
  revenue: number;
  totalBudget: number;
  hasAffiliate: boolean;
}

export interface BudgetChartData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

/**
 * Calculate budget breakdown for a project
 */
export function calculateBudgetBreakdown(
  budget: Budget | null,
  project: Project | null
): BudgetBreakdown {
  if (!budget) {
    return {
      affiliateCommission: 0,
      miscellaneousCosts: 0,
      revenue: 0,
      totalBudget: 0,
      hasAffiliate: false,
    };
  }

  const totalBudget = budget.ActualAmount;
  const hasAffiliate = budget.has_affiliate && !!project?.affiliate_user;

  // Calculate affiliate commission (10% if affiliate exists)
  const affiliateCommission = hasAffiliate
    ? budget.AffiliateCommission || totalBudget * 0.1
    : 0;

  // Calculate miscellaneous costs from expense_details
  const miscellaneousCosts = calculateMiscellaneousCosts(budget);

  // Calculate revenue (remaining after commission and misc costs)
  const revenue = Math.max(
    0,
    totalBudget - affiliateCommission - miscellaneousCosts
  );

  return {
    affiliateCommission,
    miscellaneousCosts,
    revenue,
    totalBudget,
    hasAffiliate,
  };
}

/**
 * Calculate miscellaneous costs from budget expense details
 */
export function calculateMiscellaneousCosts(budget: Budget): number {
  if (!budget.expense_details) {
    return 0;
  }

  try {
    const expenses = Array.isArray(budget.expense_details)
      ? budget.expense_details
      : [];

    return expenses.reduce((total: number, expense: unknown) => {
      const amount =
        typeof expense === 'object' &&
        expense !== null &&
        'amount' in expense &&
        typeof (expense as { amount: unknown }).amount === 'number'
          ? (expense as { amount: number }).amount
          : 0;
      return total + amount;
    }, 0);
  } catch (error) {
    console.error('Error calculating miscellaneous costs:', error);
    return 0;
  }
}

/**
 * Convert budget breakdown to chart data format
 */
export function budgetBreakdownToChartData(
  breakdown: BudgetBreakdown
): BudgetChartData[] {
  const {
    affiliateCommission,
    miscellaneousCosts,
    revenue,
    totalBudget,
    hasAffiliate,
  } = breakdown;

  if (totalBudget === 0) {
    return [];
  }

  const chartData: BudgetChartData[] = [];

  // Add affiliate commission if applicable
  if (hasAffiliate && affiliateCommission > 0) {
    chartData.push({
      name: 'Affiliate Commission',
      value: affiliateCommission,
      percentage: Math.round((affiliateCommission / totalBudget) * 100),
      color: '#f59e0b', // amber-500
    });
  }

  // Add miscellaneous costs if any
  if (miscellaneousCosts > 0) {
    chartData.push({
      name: 'Miscellaneous Costs',
      value: miscellaneousCosts,
      percentage: Math.round((miscellaneousCosts / totalBudget) * 100),
      color: '#ef4444', // red-500
    });
  }

  // Add revenue (remaining amount)
  if (revenue > 0) {
    chartData.push({
      name: 'Revenue',
      value: revenue,
      percentage: Math.round((revenue / totalBudget) * 100),
      color: '#22c55e', // green-500
    });
  }

  return chartData;
}

/**
 * Format currency value for display
 */
export function formatCurrency(
  amount: number,
  currency: string = 'USD',
  locale: string = 'en-US'
): string {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch {
    // Fallback formatting if currency is not supported
    return `${currency} ${amount.toFixed(2)}`;
  }
}

/**
 * Get budget status color
 */
export function getBudgetStatusColor(status: string): string {
  switch (status) {
    case 'draft':
      return '#6b7280'; // gray-500
    case 'approved':
      return '#22c55e'; // green-500
    case 'locked':
      return '#f59e0b'; // amber-500
    case 'spent':
      return '#ef4444'; // red-500
    default:
      return '#6b7280'; // gray-500
  }
}

/**
 * Calculate budget utilization percentage
 */
export function calculateBudgetUtilization(budget: Budget): number {
  if (budget.ActualAmount === 0) return 0;

  const spent = budget.ActualAmount - budget.CurrentAmount;
  return Math.round((spent / budget.ActualAmount) * 100);
}

/**
 * Check if budget is over allocated
 */
export function isBudgetOverAllocated(breakdown: BudgetBreakdown): boolean {
  const { affiliateCommission, miscellaneousCosts, totalBudget } = breakdown;
  return affiliateCommission + miscellaneousCosts > totalBudget;
}

/**
 * Get budget health status based on utilization and allocation
 */
export function getBudgetHealthStatus(
  budget: Budget,
  breakdown: BudgetBreakdown
): 'healthy' | 'warning' | 'critical' {
  const utilization = calculateBudgetUtilization(budget);
  const isOverAllocated = isBudgetOverAllocated(breakdown);

  if (isOverAllocated || utilization > 90) {
    return 'critical';
  } else if (utilization > 75) {
    return 'warning';
  } else {
    return 'healthy';
  }
}
