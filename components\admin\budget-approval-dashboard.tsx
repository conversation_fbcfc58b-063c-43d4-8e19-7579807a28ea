'use client';

import {
  Alert<PERSON>riangle,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  Search,
  Users,
  XCircle,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import { BudgetApprovalSystem } from '@/components/budgets/budget-approval-system';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useBudgets,
  useClients,
  useMembers,
  useProjects,
} from '@/hooks/use-db';
import type { Budget } from '@/lib/supabase/database-modules';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/format-currency';

interface BudgetApprovalDashboardProps {
  className?: string;
}

export function BudgetApprovalDashboard({
  className,
}: BudgetApprovalDashboardProps) {
  const { budgets, approveBudget } = useBudgets();
  const { projects } = useProjects();
  const { clients } = useClients();
  const { members } = useMembers();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [amountFilter, setAmountFilter] = useState<string>('all');
  const [selectedBudget, setSelectedBudget] = useState<Budget | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);

  // Filter budgets for approval dashboard
  const filteredBudgets = useMemo(() => {
    let filtered = budgets;

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter((budget) => {
        const project = projects.find((p) => p.id === budget.ProjectId);
        const client = clients.find((c) => c.id === budget.ClientId);

        return (
          project?.name.toLowerCase().includes(term) ||
          client?.name.toLowerCase().includes(term) ||
          budget.Category.toLowerCase().includes(term)
        );
      });
    }

    // Filter by status
    if (statusFilter !== 'all') {
      if (statusFilter === 'pending') {
        filtered = filtered.filter((b) => b.Status === 'draft');
      } else {
        filtered = filtered.filter((b) => b.Status === statusFilter);
      }
    }

    // Filter by amount range
    if (amountFilter !== 'all') {
      switch (amountFilter) {
        case 'small':
          filtered = filtered.filter((b) => b.ActualAmount < 1000);
          break;
        case 'medium':
          filtered = filtered.filter(
            (b) => b.ActualAmount >= 1000 && b.ActualAmount < 5000
          );
          break;
        case 'large':
          filtered = filtered.filter((b) => b.ActualAmount >= 5000);
          break;
      }
    }

    return filtered.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [budgets, projects, clients, searchTerm, statusFilter, amountFilter]);

  // Calculate approval analytics
  const approvalAnalytics = useMemo(() => {
    const pendingBudgets = budgets.filter((b) => b.Status === 'draft');
    const approvedBudgets = budgets.filter((b) => b.Status === 'approved');
    const rejectedBudgets = budgets.filter((b) => b.Status === 'locked');

    const totalPendingAmount = pendingBudgets.reduce(
      (sum, b) => sum + b.ActualAmount,
      0
    );
    const totalApprovedAmount = approvedBudgets.reduce(
      (sum, b) => sum + b.ActualAmount,
      0
    );

    // Categorize by approval complexity
    const smallBudgets = pendingBudgets.filter((b) => b.ActualAmount < 1000);
    const mediumBudgets = pendingBudgets.filter(
      (b) => b.ActualAmount >= 1000 && b.ActualAmount < 5000
    );
    const largeBudgets = pendingBudgets.filter((b) => b.ActualAmount >= 5000);

    // Overdue budgets (created more than 7 days ago and still pending)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const overdueBudgets = pendingBudgets.filter(
      (b) => new Date(b.created_at) < sevenDaysAgo
    );

    return {
      totalPending: pendingBudgets.length,
      totalApproved: approvedBudgets.length,
      totalRejected: rejectedBudgets.length,
      totalPendingAmount,
      totalApprovedAmount,
      smallBudgets: smallBudgets.length,
      mediumBudgets: mediumBudgets.length,
      largeBudgets: largeBudgets.length,
      overdueBudgets: overdueBudgets.length,
    };
  }, [budgets]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'locked':
        return 'bg-blue-100 text-blue-800';
      case 'spent':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className='h-4 w-4' />;
      case 'draft':
        return <Clock className='h-4 w-4' />;
      case 'rejected':
        return <XCircle className='h-4 w-4' />;
      case 'locked':
        return <AlertTriangle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  const getApprovalComplexity = (amount: number) => {
    if (amount < 1000)
      return {
        level: 'Simple',
        color: 'bg-green-100 text-green-800',
        steps: 1,
      };
    if (amount < 5000)
      return {
        level: 'Standard',
        color: 'bg-yellow-100 text-yellow-800',
        steps: 2,
      };
    return { level: 'Complex', color: 'bg-red-100 text-red-800', steps: 3 };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getProjectName = (projectId: string) => {
    const project = projects.find((p) => p.id === projectId);
    return project?.name || 'Unknown Project';
  };

  const getClientName = (clientId: string | null) => {
    if (!clientId) return 'No Client';
    const client = clients.find((c) => c.id === clientId);
    return client?.name || 'Unknown Client';
  };

  const quickApprove = async (budget: Budget) => {
    try {
      // For simple budgets under $1000, allow quick approval
      if (budget.ActualAmount < 1000) {
        await approveBudget(budget.id, 'system');
        toast.success('Budget approved successfully');
      } else {
        toast.info('This budget requires multi-step approval');
      }
    } catch (error) {
      toast.error('Failed to approve budget');
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Analytics Overview */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Pending Approvals
            </CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {approvalAnalytics.totalPending}
            </div>
            <p className='text-xs text-muted-foreground'>
              {formatCurrency(approvalAnalytics.totalPendingAmount, 'USD')}{' '}
              total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Approved This Month
            </CardTitle>
            <CheckCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {approvalAnalytics.totalApproved}
            </div>
            <p className='text-xs text-muted-foreground'>
              {formatCurrency(approvalAnalytics.totalApprovedAmount, 'USD')}{' '}
              approved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Overdue Reviews
            </CardTitle>
            <AlertTriangle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-red-600'>
              {approvalAnalytics.overdueBudgets}
            </div>
            <p className='text-xs text-muted-foreground'>Pending over 7 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Complex Approvals
            </CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {approvalAnalytics.largeBudgets}
            </div>
            <p className='text-xs text-muted-foreground'>
              Require 3-step approval
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Approval Queue */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle className='flex items-center gap-2'>
                <DollarSign className='h-5 w-5' />
                Budget Approval Queue
              </CardTitle>
              <CardDescription>
                Manage budget approvals and workflow
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className='flex flex-col sm:flex-row gap-4 mb-6'>
            <div className='flex-1'>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search budgets...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className='pl-10'
                />
              </div>
            </div>
            <div className='flex gap-2'>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Status</SelectItem>
                  <SelectItem value='pending'>Pending</SelectItem>
                  <SelectItem value='approved'>Approved</SelectItem>
                  <SelectItem value='rejected'>Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select value={amountFilter} onValueChange={setAmountFilter}>
                <SelectTrigger className='w-[140px]'>
                  <SelectValue placeholder='Amount' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Amounts</SelectItem>
                  <SelectItem value='small'>Under $1K</SelectItem>
                  <SelectItem value='medium'>$1K - $5K</SelectItem>
                  <SelectItem value='large'>Over $5K</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Budget Table */}
          {filteredBudgets.length > 0 ? (
            <div className='border rounded-lg'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Complexity</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBudgets.map((budget) => {
                    const complexity = getApprovalComplexity(
                      budget.ActualAmount
                    );
                    const isOverdue =
                      new Date(budget.created_at) <
                      new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

                    return (
                      <TableRow
                        key={budget.id}
                        className={isOverdue ? 'bg-red-50' : ''}
                      >
                        <TableCell>
                          <div>
                            <div className='font-medium'>
                              {getProjectName(budget.ProjectId)}
                            </div>
                            <div className='text-sm text-muted-foreground'>
                              {getClientName(budget.ClientId)} •{' '}
                              {budget.Category}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='font-medium'>
                            {formatCurrency(
                              budget.ActualAmount,
                              budget.Currency
                            )}
                          </div>
                          <div className='text-sm text-muted-foreground'>
                            {budget.has_affiliate && 'Affiliate Commission'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={complexity.color}>
                            {complexity.level}
                          </Badge>
                          <div className='text-xs text-muted-foreground mt-1'>
                            {complexity.steps} approval steps
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            {getStatusIcon(budget.Status)}
                            <Badge className={getStatusColor(budget.Status)}>
                              {budget.Status === 'draft'
                                ? 'Pending'
                                : budget.Status}
                            </Badge>
                          </div>
                          {isOverdue && (
                            <div className='text-xs text-red-600 mt-1'>
                              Overdue
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            <Calendar className='h-4 w-4 text-muted-foreground' />
                            {formatDate(budget.created_at)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='flex gap-1'>
                            <Button
                              size='sm'
                              variant='ghost'
                              className='h-8 w-8 p-0'
                              onClick={() => {
                                setSelectedBudget(budget);
                                setIsDetailDialogOpen(true);
                              }}
                            >
                              <Eye className='h-4 w-4' />
                            </Button>
                            {budget.Status === 'draft' &&
                              budget.ActualAmount < 1000 && (
                                <Button
                                  size='sm'
                                  variant='outline'
                                  className='h-8 px-2 text-xs'
                                  onClick={() => quickApprove(budget)}
                                >
                                  Quick Approve
                                </Button>
                              )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className='text-center py-8 text-muted-foreground'>
              <DollarSign className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>No budgets found</p>
              <p className='text-sm'>
                {searchTerm || statusFilter !== 'all' || amountFilter !== 'all'
                  ? 'Try adjusting your filters'
                  : 'No budgets pending approval'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Budget Detail Dialog */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>Budget Approval Details</DialogTitle>
            <DialogDescription>
              Complete budget approval workflow and details
            </DialogDescription>
          </DialogHeader>
          {selectedBudget && <BudgetApprovalSystem budget={selectedBudget} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}
