'use client';

import { 
  Book, 
  FileText, 
  Code, 
  Database,
  Users,
  Settings,
  Workflow,
  Shield,
  Zap,
  ExternalLink,
  Download,
  Search
} from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils/cn';

interface DocumentationSection {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  content: string;
  tags: string[];
  lastUpdated: string;
}

interface SystemDocumentationProps {
  className?: string;
}

export function SystemDocumentation({ className }: SystemDocumentationProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSection, setSelectedSection] = useState<string>('overview');

  const documentationSections: DocumentationSection[] = [
    {
      id: 'overview',
      title: 'System Overview',
      description: 'Complete overview of the project management system',
      icon: Book,
      tags: ['overview', 'architecture', 'features'],
      lastUpdated: '2024-01-15',
      content: `
# Project Management System Overview

## System Architecture
The project management system is built with a modern, scalable architecture:

### Core Components
- **Frontend**: Next.js 14 with TypeScript and Tailwind CSS
- **Backend**: Supabase with PostgreSQL database
- **Authentication**: Supabase Auth with role-based access control
- **UI Framework**: shadcn/ui components with Radix UI primitives

### Key Features
1. **Proposal-to-Client-Budget Integration**
   - Automated workflow from proposal approval to project creation
   - Intelligent client matching and creation
   - Automatic budget generation with commission calculation

2. **Budget Management System**
   - Comprehensive budget creation and tracking
   - Multi-step approval workflows
   - Expense tracking with real-time updates
   - Analytics and reporting dashboards

3. **Client Management Enhancement**
   - Complete client relationship management
   - Project and budget association tracking
   - Client analytics and insights
   - Communication history and preferences

4. **Affiliate Commission System**
   - Automatic commission calculation (10% default)
   - Commission tracking and analytics
   - Payout request and management system
   - Performance metrics and reporting

5. **Budget Approval Workflow**
   - Multi-step approval based on budget amount
   - Role-based approval authority
   - Notification system for pending approvals
   - Complete audit trail and history

### User Roles
- **Admin**: Full system access and management
- **Manager**: Project and budget management
- **Finance**: Financial oversight and approvals
- **Affiliate**: Proposal submission and commission tracking
- **User**: Basic project access and collaboration
      `
    },
    {
      id: 'api',
      title: 'API Documentation',
      description: 'Database hooks and service layer documentation',
      icon: Code,
      tags: ['api', 'hooks', 'services', 'database'],
      lastUpdated: '2024-01-15',
      content: `
# API Documentation

## Database Hooks

### useBudgets()
Comprehensive budget management hook with CRUD operations and analytics.

\`\`\`typescript
const {
  budgets,           // All budgets array
  loading,           // Loading state
  addBudget,         // Create new budget
  updateBudget,      // Update existing budget
  deleteBudget,      // Delete budget
  approveBudget,     // Approve budget workflow
  addExpense,        // Add expense to budget
  getBudgetAnalytics // Get budget analytics
} = useBudgets();
\`\`\`

### useClients()
Client management with relationship tracking and analytics.

\`\`\`typescript
const {
  clients,           // All clients array
  loading,           // Loading state
  addClient,         // Create new client
  updateClient,      // Update existing client
  deleteClient,      // Delete client
  getClientAnalytics // Get client analytics
} = useClients();
\`\`\`

### useProposals()
Proposal management with approval workflow integration.

\`\`\`typescript
const {
  proposals,         // All proposals array
  loading,           // Loading state
  addProposal,       // Submit new proposal
  updateProposal,    // Update proposal
  approveProposal,   // Approve proposal
  rejectProposal     // Reject proposal
} = useProposals();
\`\`\`

## Service Layer

### ProposalProcessingService
Handles the complete proposal-to-budget workflow.

\`\`\`typescript
// Validate and extract proposal data
const validation = ProposalProcessingService.validateAndExtractProposalData(proposal);

// Process approved proposal
const result = await ProposalProcessingService.processApprovedProposal(proposal, options);

// Generate processing report
const report = ProposalProcessingService.generateProcessingReport(proposal, result);
\`\`\`

### Key Methods
- \`validateAndExtractProposalData()\`: Validates proposal data
- \`processApprovedProposal()\`: Complete workflow processing
- \`generateProcessingReport()\`: Creates processing summary
      `
    },
    {
      id: 'database',
      title: 'Database Schema',
      description: 'Complete database structure and relationships',
      icon: Database,
      tags: ['database', 'schema', 'relationships', 'tables'],
      lastUpdated: '2024-01-15',
      content: `
# Database Schema

## Core Tables

### budgets
Primary budget management table with comprehensive tracking.

\`\`\`sql
CREATE TABLE budgets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ProjectId UUID REFERENCES projects(id),
  ClientId UUID REFERENCES clients(id),
  ActualAmount DECIMAL(10,2) NOT NULL,
  CurrentAmount DECIMAL(10,2) NOT NULL,
  Currency VARCHAR(3) DEFAULT 'USD',
  Category VARCHAR(50) NOT NULL,
  Status VARCHAR(20) DEFAULT 'draft',
  has_affiliate BOOLEAN DEFAULT false,
  affiliateId UUID REFERENCES members(id),
  AffiliateCommission DECIMAL(10,2),
  StartDate DATE NOT NULL,
  EndDate DATE NOT NULL,
  ApprovedBy UUID REFERENCES members(id),
  ApprovalDate TIMESTAMP,
  Notes TEXT,
  expense_details JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

### clients
Client management with comprehensive contact information.

\`\`\`sql
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(50),
  company_name VARCHAR(255),
  industry VARCHAR(100),
  contact_person VARCHAR(255),
  address JSONB,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES members(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

### proposals
Proposal management with affiliate integration.

\`\`\`sql
CREATE TABLE proposals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_name VARCHAR(255) NOT NULL,
  client_email VARCHAR(255) NOT NULL,
  client_id UUID REFERENCES clients(id),
  affiliate_proposal JSONB NOT NULL,
  user_id UUID REFERENCES members(id),
  user_email VARCHAR(255) NOT NULL,
  is_approved BOOLEAN,
  approval_notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

## Relationships

### Budget Relationships
- budgets → projects (ProjectId)
- budgets → clients (ClientId)
- budgets → members (affiliateId, ApprovedBy)

### Client Relationships
- clients → projects (client_id)
- clients → budgets (ClientId)
- clients → proposals (client_id)

### Proposal Relationships
- proposals → clients (client_id)
- proposals → members (user_id)

## Indexes
Key indexes for performance optimization:

\`\`\`sql
-- Budget indexes
CREATE INDEX idx_budgets_project ON budgets(ProjectId);
CREATE INDEX idx_budgets_client ON budgets(ClientId);
CREATE INDEX idx_budgets_affiliate ON budgets(affiliateId);
CREATE INDEX idx_budgets_status ON budgets(Status);

-- Client indexes
CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_clients_active ON clients(is_active);

-- Proposal indexes
CREATE INDEX idx_proposals_client_email ON proposals(client_email);
CREATE INDEX idx_proposals_user ON proposals(user_id);
CREATE INDEX idx_proposals_approved ON proposals(is_approved);
\`\`\`
      `
    },
    {
      id: 'workflows',
      title: 'Business Workflows',
      description: 'Complete business process documentation',
      icon: Workflow,
      tags: ['workflows', 'processes', 'business', 'automation'],
      lastUpdated: '2024-01-15',
      content: `
# Business Workflows

## Proposal-to-Budget Workflow

### 1. Proposal Submission
- Affiliate submits proposal through form
- System validates proposal data
- Proposal stored with pending status

### 2. Admin Review
- Admin reviews proposal details
- Validation checks performed
- Approval or rejection decision

### 3. Automatic Processing (On Approval)
- **Client Creation/Lookup**
  - Check for existing client by email
  - Create new client if not found
  - Associate client with proposal

- **Project Creation**
  - Generate project from proposal data
  - Link project to client
  - Set project parameters and timeline

- **Budget Creation**
  - Create budget based on proposal amount
  - Calculate affiliate commission (10% default)
  - Set budget status to draft
  - Link budget to project and client

### 4. Budget Approval Workflow
- **Amount-based Approval Steps**
  - Under $1,000: Manager approval only
  - $1,000-$5,000: Manager + Admin approval
  - Over $5,000: Manager + Admin + Finance approval

- **Sequential Processing**
  - Each step must be completed in order
  - Role-based approval authority
  - Notification system for pending approvals

### 5. Project Execution
- Budget becomes active after approval
- Expense tracking begins
- Commission tracking for affiliates
- Progress monitoring and reporting

## Commission Workflow

### 1. Commission Calculation
- Automatic 10% commission on budget amount
- Override capability for custom rates
- Commission tied to project completion

### 2. Commission Tracking
- Real-time commission analytics
- Performance metrics and reporting
- Conversion rate tracking

### 3. Payout Process
- Commission available when project status = 'spent'
- Affiliate submits payout request
- Admin processes payout
- Payment method selection (bank, PayPal, Stripe)

## Approval Workflow

### 1. Budget Submission
- Budget created in draft status
- Automatic notification to approvers
- Approval complexity determined by amount

### 2. Multi-step Approval
- Sequential approval process
- Role-based approval authority
- Comments and feedback system

### 3. Status Management
- Real-time status updates
- Notification system
- Audit trail maintenance

### 4. Completion
- Final approval activates budget
- Project execution begins
- Expense tracking enabled
      `
    },
    {
      id: 'security',
      title: 'Security & Permissions',
      description: 'Security model and access control documentation',
      icon: Shield,
      tags: ['security', 'permissions', 'roles', 'access'],
      lastUpdated: '2024-01-15',
      content: `
# Security & Permissions

## Role-Based Access Control (RBAC)

### User Roles

#### Admin
- **Full System Access**: Complete administrative control
- **User Management**: Create, update, delete users
- **Budget Approval**: Can approve any budget amount
- **System Configuration**: Modify system settings
- **Data Access**: View all data across the system

#### Manager
- **Project Management**: Create and manage projects
- **Budget Management**: Create and manage budgets
- **Approval Authority**: Approve budgets up to $5,000
- **Team Oversight**: Manage team members and assignments
- **Reporting**: Access to management reports

#### Finance
- **Financial Oversight**: Review all financial data
- **Budget Approval**: Required for budgets over $5,000
- **Commission Management**: Oversee affiliate commissions
- **Financial Reporting**: Access to financial analytics
- **Audit Trail**: Review all financial transactions

#### Affiliate
- **Proposal Submission**: Submit client proposals
- **Commission Tracking**: View personal commission data
- **Payout Requests**: Request commission payouts
- **Performance Analytics**: View personal metrics
- **Limited Access**: Only own data and related information

#### User
- **Project Access**: View assigned projects
- **Basic Collaboration**: Comment and update tasks
- **Limited Reporting**: Personal activity reports
- **Profile Management**: Update own profile information

## Data Security

### Authentication
- **Supabase Auth**: Secure authentication system
- **JWT Tokens**: Stateless authentication
- **Session Management**: Automatic session handling
- **Password Security**: Encrypted password storage

### Authorization
- **Row Level Security (RLS)**: Database-level access control
- **API Security**: Protected API endpoints
- **Role Validation**: Server-side role verification
- **Data Isolation**: Users see only authorized data

### Data Protection
- **Encryption**: Data encrypted at rest and in transit
- **Audit Logging**: Complete activity audit trail
- **Data Backup**: Regular automated backups
- **Privacy Compliance**: GDPR and privacy law compliance

## Security Policies

### RLS Policies

#### Budgets Table
\`\`\`sql
-- Users can only see budgets they're authorized to view
CREATE POLICY budget_access ON budgets
FOR SELECT USING (
  auth.role() = 'Admin' OR
  (auth.role() = 'Manager' AND created_by = auth.uid()) OR
  (auth.role() = 'Finance') OR
  (auth.role() = 'Affiliate' AND affiliateId = auth.uid())
);
\`\`\`

#### Clients Table
\`\`\`sql
-- Admins and Managers can access all clients
CREATE POLICY client_access ON clients
FOR SELECT USING (
  auth.role() IN ('Admin', 'Manager', 'Finance')
);
\`\`\`

#### Proposals Table
\`\`\`sql
-- Users can see their own proposals, admins see all
CREATE POLICY proposal_access ON proposals
FOR SELECT USING (
  auth.role() = 'Admin' OR
  user_id = auth.uid()
);
\`\`\`

### API Security
- **Rate Limiting**: Prevent API abuse
- **Input Validation**: Sanitize all inputs
- **SQL Injection Protection**: Parameterized queries
- **XSS Prevention**: Output encoding and CSP headers

### Best Practices
- **Principle of Least Privilege**: Minimum required access
- **Regular Security Reviews**: Periodic access audits
- **Security Training**: User security awareness
- **Incident Response**: Security incident procedures
      `
    },
    {
      id: 'deployment',
      title: 'Deployment Guide',
      description: 'Production deployment and configuration guide',
      icon: Zap,
      tags: ['deployment', 'production', 'configuration', 'setup'],
      lastUpdated: '2024-01-15',
      content: `
# Deployment Guide

## Production Deployment

### Prerequisites
- Node.js 18+ installed
- Supabase project configured
- Domain and SSL certificate
- Environment variables configured

### Environment Configuration

#### Required Environment Variables
\`\`\`bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application Configuration
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your_nextauth_secret

# Email Configuration (Optional)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
\`\`\`

### Build and Deploy

#### 1. Install Dependencies
\`\`\`bash
npm install
\`\`\`

#### 2. Build Application
\`\`\`bash
npm run build
\`\`\`

#### 3. Start Production Server
\`\`\`bash
npm start
\`\`\`

### Database Setup

#### 1. Run Migrations
\`\`\`sql
-- Create tables and relationships
-- Apply RLS policies
-- Create indexes
-- Set up triggers
\`\`\`

#### 2. Seed Initial Data
\`\`\`sql
-- Create admin user
-- Set up default roles
-- Configure system settings
\`\`\`

### Monitoring and Maintenance

#### Performance Monitoring
- **Application Metrics**: Response times, error rates
- **Database Performance**: Query performance, connection pools
- **User Analytics**: Usage patterns, feature adoption
- **System Health**: Server resources, uptime monitoring

#### Backup Strategy
- **Database Backups**: Daily automated backups
- **File Storage**: Regular file system backups
- **Configuration Backup**: Environment and settings backup
- **Disaster Recovery**: Recovery procedures and testing

#### Security Monitoring
- **Access Logs**: Monitor user access patterns
- **Failed Logins**: Track authentication failures
- **API Usage**: Monitor API endpoint usage
- **Security Alerts**: Automated security notifications

### Scaling Considerations

#### Horizontal Scaling
- **Load Balancing**: Distribute traffic across instances
- **Database Scaling**: Read replicas and connection pooling
- **CDN Integration**: Static asset delivery optimization
- **Caching Strategy**: Redis or similar caching layer

#### Performance Optimization
- **Code Splitting**: Optimize bundle sizes
- **Image Optimization**: Compress and optimize images
- **Database Optimization**: Query optimization and indexing
- **Monitoring**: Continuous performance monitoring

### Troubleshooting

#### Common Issues
- **Database Connection**: Check connection strings and credentials
- **Authentication**: Verify Supabase configuration
- **Build Errors**: Check dependencies and TypeScript errors
- **Performance**: Monitor database queries and API responses

#### Debug Tools
- **Application Logs**: Structured logging with levels
- **Database Logs**: Query performance and errors
- **Browser DevTools**: Client-side debugging
- **Monitoring Dashboards**: Real-time system metrics
      `
    }
  ];

  const filteredSections = documentationSections.filter(section =>
    searchTerm === '' ||
    section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    section.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    section.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
    section.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedSectionData = documentationSections.find(s => s.id === selectedSection);

  const exportDocumentation = () => {
    const allContent = documentationSections.map(section => 
      `# ${section.title}\n\n${section.description}\n\n${section.content}\n\n---\n\n`
    ).join('');
    
    const blob = new Blob([allContent], { type: 'text/markdown' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'system-documentation.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Documentation Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Book className="h-5 w-5" />
                System Documentation
              </CardTitle>
              <CardDescription>
                Comprehensive documentation for the project management system
              </CardDescription>
            </div>
            <Button 
              onClick={exportDocumentation}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documentation..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              {filteredSections.length} of {documentationSections.length} sections
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Documentation Navigation */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">Sections</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px]">
              <div className="space-y-2">
                {filteredSections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <Button
                      key={section.id}
                      variant={selectedSection === section.id ? "default" : "ghost"}
                      className="w-full justify-start h-auto p-3"
                      onClick={() => setSelectedSection(section.id)}
                    >
                      <div className="flex items-start gap-3">
                        <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        <div className="text-left">
                          <div className="font-medium text-sm">{section.title}</div>
                          <div className="text-xs text-muted-foreground">
                            {section.description}
                          </div>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {section.tags.slice(0, 2).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Documentation Content */}
        <Card className="lg:col-span-3">
          <CardHeader>
            {selectedSectionData && (
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <selectedSectionData.icon className="h-5 w-5" />
                    {selectedSectionData.title}
                  </CardTitle>
                  <CardDescription>
                    {selectedSectionData.description}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    Updated: {selectedSectionData.lastUpdated}
                  </Badge>
                  <Button size="sm" variant="outline">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardHeader>
          <CardContent>
            {selectedSectionData ? (
              <ScrollArea className="h-[600px]">
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                    {selectedSectionData.content}
                  </pre>
                </div>
              </ScrollArea>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Select a documentation section to view its content</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
