'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { useTasks, useProfile } from '@/hooks/use-db';
import type { Task } from '@/lib/supabase/database-modules';

// Create volunteer-specific columns (similar to collaborator columns but for volunteers)
import { columns } from '@/components/tables/volunteer-tasks/columns';

export default function VolunteerTasksPage() {
  const { getCollaboratorTasks } = useTasks();
  const { profile } = useProfile();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!profile?.id) return;

    setLoading(true);
    setError(null);

    // Use the same method as collaborators since volunteers have similar access patterns
    getCollaboratorTasks(profile.id)
      .then((volunteerTasks) => {
        setTasks(volunteerTasks);
      })
      .catch((error) => {
        console.error('Error fetching volunteer tasks:', error);
        setError('Failed to load tasks');
        toast.error('Failed to load tasks');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [profile?.id, getCollaboratorTasks]);

  if (error) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <p className='font-medium text-sm'>Tasks</p>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-32'>
            <p className='text-muted-foreground'>{error}</p>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Tasks</p>
      </header>
      <section className='flex-1 p-6'>
        <DataTable columns={columns} data={tasks} isLoading={loading} />
      </section>
    </main>
  );
}
