'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useMembers, useProfile } from '@/hooks/use-db';

const editPortfolioSchema = z.object({
  full_name: z.string().min(2, 'Full name must be at least 2 characters'),
  username: z.string().optional(),
  avatar_url: z
    .string()
    .url('Please enter a valid URL')
    .optional()
    .or(z.literal('')),
});

type EditPortfolioFormData = z.infer<typeof editPortfolioSchema>;

export function EditPortfolioForm() {
  const router = useRouter();
  const { profile } = useProfile();
  const { updateMember } = useMembers();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<EditPortfolioFormData>({
    resolver: zodResolver(editPortfolioSchema),
    defaultValues: {
      full_name: profile?.full_name || profile?.name || '',
      username: profile?.username || '',
      avatar_url: profile?.avatar_url || '',
    },
  });

  // Update form values when profile loads
  useEffect(() => {
    if (profile) {
      form.reset({
        full_name: profile.full_name || profile.name || '',
        username: profile.username || '',
        avatar_url: profile.avatar_url || '',
      });
    }
  }, [profile, form]);

  const onSubmit = (data: EditPortfolioFormData) => {
    if (!profile?.id) {
      toast.error('User not authenticated');
      return;
    }

    setIsSubmitting(true);

    const updatePromise = updateMember(profile.id, {
      full_name: data.full_name,
      username: data.username || null,
      avatar_url: data.avatar_url || null,
    });

    updatePromise
      .then(() => {
        router.push('/affiliate/portfolio');
      })
      .catch((error) => {
        console.error('Error updating profile:', error);
      })
      .finally(() => {
        setIsSubmitting(false);
      });

    toast.promise(updatePromise, {
      loading: 'Updating profile...',
      success: 'Profile updated successfully!',
      error: (error) =>
        `Failed to update profile: ${error?.message || 'Unknown error'}`,
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const watchedAvatarUrl = form.watch('avatar_url');
  const watchedFullName = form.watch('full_name');

  if (!profile) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardHeader>
          <div className='h-6 w-48 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
          <div className='h-4 w-32 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse' />
        </CardHeader>
        <CardContent className='space-y-4'>
          {[1, 2, 3, 4].map((i) => (
            <div
              key={i}
              className='h-10 w-full bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse'
            />
          ))}
        </CardContent>
      </Card>
    );
  }

  if (!profile) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='text-center py-12'>
          <p className='text-destructive font-medium'>
            Unable to load profile information
          </p>
          <p className='text-sm text-muted-foreground mt-2'>
            Please try refreshing the page.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className='max-w-2xl mx-auto'>
      <CardHeader>
        <CardTitle>Edit Portfolio</CardTitle>
        <CardDescription>
          Update your profile information and portfolio details.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            {/* Avatar Preview */}
            <div className='flex items-center space-x-4'>
              <Avatar className='w-16 h-16'>
                <AvatarImage
                  src={watchedAvatarUrl || undefined}
                  alt={watchedFullName}
                />
                <AvatarFallback className='text-lg'>
                  {getInitials(watchedFullName || 'User')}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className='text-sm font-medium'>Profile Picture</p>
                <p className='text-xs text-muted-foreground'>
                  Update your avatar URL below to change your profile picture
                </p>
              </div>
            </div>

            <FormField
              control={form.control}
              name='full_name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name *</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter your full name' {...field} />
                  </FormControl>
                  <FormDescription>
                    This is your display name that will be shown to others
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='username'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Username</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter a username (optional)'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    A unique username for your profile (optional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='avatar_url'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Avatar URL</FormLabel>
                  <FormControl>
                    <Input
                      type='url'
                      placeholder='https://example.com/your-avatar.jpg'
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    URL to your profile picture (optional)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='flex justify-end space-x-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => router.back()}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
