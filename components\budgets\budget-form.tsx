'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { DollarSign, Save, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { ClientSelector } from '@/components/projects/client-selector';
import { DatePicker } from '@/components/projects/date-picker';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useBudgets, useMembers, useProjects } from '@/hooks/use-db';
import type {
  Budget,
  CreateBudgetInput,
} from '@/lib/supabase/database-modules';
import { getCurrencyOptions, SUPPORTED_CURRENCIES } from '@/lib/utils/currency';

// Zod schema for budget form validation
const budgetFormSchema = z.object({
  ProjectId: z.string().min(1, 'Project is required'),
  ClientId: z.string().optional(),
  ActualAmount: z.number().min(0.01, 'Budget amount must be greater than 0'),
  CurrentAmount: z.number().min(0, 'Current amount must be non-negative'),
  Currency: z.enum(
    SUPPORTED_CURRENCIES.map((c) => c.code) as [string, ...string[]]
  ),
  Category: z.enum([
    'marketing',
    'development',
    'consulting',
    'operations',
    'other',
  ]),
  Status: z.enum(['draft', 'approved', 'locked', 'spent']).optional(),
  has_affiliate: z.boolean(),
  affiliateId: z.string().optional(),
  AffiliateCommission: z.number().min(0).optional(),
  has_collaborator: z.boolean(),
  StartDate: z.string().min(1, 'Start date is required'),
  EndDate: z.string().min(1, 'End date is required'),
  Notes: z.string().optional(),
});

type BudgetFormData = z.infer<typeof budgetFormSchema>;

interface BudgetFormProps {
  budget?: Budget;
  projectId?: string;
  clientId?: string;
  onSuccess?: (budget: Budget) => void;
  onCancel?: () => void;
  className?: string;
}

export function BudgetForm({
  budget,
  projectId,
  clientId,
  onSuccess,
  onCancel,
  className,
}: BudgetFormProps) {
  const { addBudget, updateBudget } = useBudgets();
  const { projects } = useProjects();
  const { members } = useMembers();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!budget;
  const affiliateMembers = members.filter((m) => m.role === 'Affiliate');

  const form = useForm<BudgetFormData>({
    resolver: zodResolver(budgetFormSchema),
    defaultValues: {
      ProjectId: budget?.ProjectId || projectId || '',
      ClientId: budget?.ClientId || clientId || '',
      ActualAmount: budget?.ActualAmount || 0,
      CurrentAmount: budget?.CurrentAmount || budget?.ActualAmount || 0,
      Currency: budget?.Currency || 'USD',
      Category: budget?.Category || 'development',
      Status: budget?.Status || 'draft',
      has_affiliate: budget?.has_affiliate || false,
      affiliateId: budget?.affiliateId || '',
      AffiliateCommission: budget?.AffiliateCommission || 0,
      has_collaborator: budget?.has_collaborator || false,
      StartDate: budget?.StartDate || new Date().toISOString().split('T')[0],
      EndDate:
        budget?.EndDate ||
        new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0],
      Notes: budget?.Notes || '',
    },
  });

  const watchHasAffiliate = form.watch('has_affiliate');
  const watchActualAmount = form.watch('ActualAmount');

  // Auto-calculate commission when amount or affiliate status changes
  const handleAffiliateToggle = (hasAffiliate: boolean) => {
    if (hasAffiliate && watchActualAmount > 0) {
      const defaultCommission = watchActualAmount * 0.1; // 10% default
      form.setValue('AffiliateCommission', defaultCommission);
    } else {
      form.setValue('AffiliateCommission', 0);
    }
  };

  const onSubmit = async (data: BudgetFormData) => {
    setIsSubmitting(true);

    try {
      // Validate dates
      const startDate = new Date(data.StartDate);
      const endDate = new Date(data.EndDate);
      if (endDate <= startDate) {
        toast.error('End date must be after start date');
        return;
      }

      // Validate current amount doesn't exceed actual amount
      if (data.CurrentAmount > data.ActualAmount) {
        toast.error('Current amount cannot exceed total budget amount');
        return;
      }

      const budgetInput: CreateBudgetInput = {
        ProjectId: data.ProjectId,
        ClientId: data.ClientId || null,
        ActualAmount: data.ActualAmount,
        CurrentAmount: data.CurrentAmount,
        Currency: data.Currency as 'USD' | 'EUR' | 'GBP' | 'JPY',
        Category: data.Category,
        Status: data.Status || 'draft',
        has_affiliate: data.has_affiliate,
        affiliateId: data.has_affiliate ? data.affiliateId || null : null,
        AffiliateCommission: data.has_affiliate
          ? data.AffiliateCommission || null
          : null,
        has_collaborator: data.has_collaborator,
        StartDate: data.StartDate,
        EndDate: data.EndDate,
        Notes: data.Notes || null,
      };

      let result: Budget;
      if (isEditing && budget) {
        result = await updateBudget(budget.id, budgetInput);
        toast.success('Budget updated successfully');
      } else {
        result = await addBudget(budgetInput);
        toast.success('Budget created successfully');
      }

      onSuccess?.(result);
    } catch (error) {
      console.error('Error saving budget:', error);
      toast.error(
        `Failed to ${isEditing ? 'update' : 'create'} budget: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <DollarSign className='h-5 w-5' />
          {isEditing ? 'Edit Budget' : 'Create Budget'}
        </CardTitle>
        <CardDescription>
          {isEditing
            ? 'Update budget details and allocations'
            : 'Set up budget tracking for this project'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              {/* Project Selection */}
              <div className='md:col-span-2'>
                <FormField
                  control={form.control}
                  name='ProjectId'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select project' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {projects.map((project) => (
                            <SelectItem key={project.id} value={project.id}>
                              {project.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Client Selection */}
              <div className='md:col-span-2'>
                <Label>Client</Label>
                <div className='mt-2'>
                  <ClientSelector
                    value={form.watch('ClientId')}
                    onValueChange={(value) =>
                      form.setValue('ClientId', value || undefined)
                    }
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              {/* Budget Amount */}
              <div>
                <FormField
                  control={form.control}
                  name='ActualAmount'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Total Budget Amount *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type='number'
                          min='0'
                          step='0.01'
                          placeholder='0.00'
                          disabled={isSubmitting}
                          onChange={(e) => {
                            const value = Number(e.target.value);
                            field.onChange(value);
                            // Auto-update current amount if it's a new budget
                            if (!isEditing) {
                              form.setValue('CurrentAmount', value);
                            }
                            // Auto-calculate commission if affiliate is enabled
                            if (watchHasAffiliate) {
                              form.setValue('AffiliateCommission', value * 0.1);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Current Amount */}
              <div>
                <FormField
                  control={form.control}
                  name='CurrentAmount'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Amount</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type='number'
                          min='0'
                          step='0.01'
                          placeholder='0.00'
                          disabled={isSubmitting}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Amount remaining in the budget
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Currency */}
              <div>
                <FormField
                  control={form.control}
                  name='Currency'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select currency' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {getCurrencyOptions().map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Category */}
              <div>
                <FormField
                  control={form.control}
                  name='Category'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select category' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='development'>
                            Development
                          </SelectItem>
                          <SelectItem value='marketing'>Marketing</SelectItem>
                          <SelectItem value='consulting'>Consulting</SelectItem>
                          <SelectItem value='operations'>Operations</SelectItem>
                          <SelectItem value='other'>Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Affiliate Section */}
            <div className='space-y-4 p-4 border rounded-lg'>
              <FormField
                control={form.control}
                name='has_affiliate'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-center justify-between'>
                    <div className='space-y-0.5'>
                      <FormLabel className='text-base'>
                        Affiliate Commission
                      </FormLabel>
                      <FormDescription>
                        Enable affiliate commission tracking for this budget
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          handleAffiliateToggle(checked);
                        }}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {watchHasAffiliate && (
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <FormField
                    control={form.control}
                    name='affiliateId'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Affiliate</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select affiliate' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {affiliateMembers.map((member) => (
                              <SelectItem key={member.id} value={member.id}>
                                {member.full_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='AffiliateCommission'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Commission Amount</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type='number'
                            min='0'
                            step='0.01'
                            placeholder='0.00'
                            disabled={isSubmitting}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Commission amount for the affiliate
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Date Range */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div>
                <Label>Start Date *</Label>
                <div className='mt-2'>
                  <DatePicker
                    date={
                      form.watch('StartDate')
                        ? new Date(form.watch('StartDate'))
                        : undefined
                    }
                    onDateChange={(date) =>
                      form.setValue(
                        'StartDate',
                        date ? date.toISOString().split('T')[0] : ''
                      )
                    }
                  />
                </div>
              </div>

              <div>
                <Label>End Date *</Label>
                <div className='mt-2'>
                  <DatePicker
                    date={
                      form.watch('EndDate')
                        ? new Date(form.watch('EndDate'))
                        : undefined
                    }
                    onDateChange={(date) =>
                      form.setValue(
                        'EndDate',
                        date ? date.toISOString().split('T')[0] : ''
                      )
                    }
                  />
                </div>
              </div>
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name='Notes'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder='Additional notes about this budget...'
                      disabled={isSubmitting}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional notes or comments about this budget
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className='flex gap-3 pt-4'>
              <Button
                type='submit'
                disabled={isSubmitting}
                className='flex items-center gap-2'
              >
                <Save className='h-4 w-4' />
                {isSubmitting
                  ? isEditing
                    ? 'Updating...'
                    : 'Creating...'
                  : isEditing
                    ? 'Update Budget'
                    : 'Create Budget'}
              </Button>
              {onCancel && (
                <Button
                  type='button'
                  variant='outline'
                  onClick={onCancel}
                  disabled={isSubmitting}
                  className='flex items-center gap-2'
                >
                  <X className='h-4 w-4' />
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
