import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import type { IssueHealthUpdateEmailData } from '@/lib/types/email-types';

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const IssueHealthUpdate = (data: IssueHealthUpdateEmailData) => {
  const { issue, project, oldHealth, newHealth, triggeredBy } = data;

  return (
    <Html>
      <Head />
      <Preview>Issue Health Updated: {issue.title}</Preview>
      <Body style={main}>
        {/* Header Section */}
        <Container
          style={{
            ...container,
            backgroundColor: colors['100'],
          }}
        >
          <Img
            src={`${baseUrl}/thehuefactory_hero.png`}
            width='100%'
            height='auto'
            alt='Email Header Image'
          />
        </Container>

        {/* Header Transition */}
        <Container
          style={{
            margin: '0 auto',
            backgroundColor: colors['100'],
            alignItems: 'center',
            alignContent: 'center',
            textAlign: 'center',
          }}
        >
          <Section
            style={{
              backgroundColor: 'white',
              height: 20,
              borderTopLeftRadius: '16px',
              borderTopRightRadius: '16px',
            }}
          ></Section>
        </Container>

        {/* Main Content */}
        <Container style={container}>
          <Heading style={h1}>Issue Health Updated</Heading>

          <Text style={{ ...text, marginBottom: '24px' }}>
            The health status of an issue in the <strong>{project.name}</strong>{' '}
            project has been updated.
          </Text>

          {/* Issue Details */}
          <Container style={issueCard}>
            <Heading style={issueTitle}>{issue.title}</Heading>

            {issue.description && (
              <Text style={{ ...text, marginBottom: '16px' }}>
                {issue.description}
              </Text>
            )}

            {/* Health Change Highlight */}
            <Container style={healthChangeContainer}>
              <Container style={healthItem}>
                <Text style={healthLabel}>Previous Health:</Text>
                <Container
                  style={{
                    ...healthBadge,
                    backgroundColor: oldHealth.color || '#6c757d',
                  }}
                >
                  <Text style={healthText}>{oldHealth.name}</Text>
                </Container>
              </Container>

              <Text style={healthArrow}>→</Text>

              <Container style={healthItem}>
                <Text style={healthLabel}>New Health:</Text>
                <Container
                  style={{
                    ...healthBadge,
                    backgroundColor: newHealth.color || '#28a745',
                  }}
                >
                  <Text style={healthText}>{newHealth.name}</Text>
                </Container>
              </Container>
            </Container>

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Project:</Text>
              <Text style={detailValue}>{project.name}</Text>
            </Container>

            {issue.assignee && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Assigned to:</Text>
                <Text style={detailValue}>{issue.assignee.full_name}</Text>
              </Container>
            )}

            {issue.priority && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Priority:</Text>
                <Text style={detailValue}>{issue.priority.name}</Text>
              </Container>
            )}

            {issue.status && (
              <Container style={detailsContainer}>
                <Text style={detailLabel}>Status:</Text>
                <Text style={detailValue}>{issue.status.name}</Text>
              </Container>
            )}

            <Container style={detailsContainer}>
              <Text style={detailLabel}>Updated by:</Text>
              <Text style={detailValue}>{triggeredBy.full_name}</Text>
            </Container>
          </Container>

          {/* Action Button */}
          <Container style={{ textAlign: 'center', marginTop: '32px' }}>
            <Link
              href={`https://team.thehuefactory.co/admin/issues/${issue.id}`}
              target='_blank'
              style={{
                backgroundColor: colors[100],
                paddingRight: 18,
                paddingLeft: 18,
                fontWeight: 'bold',
                color: 'white',
                paddingTop: 16,
                paddingBottom: 16,
                borderRadius: 32,
                whiteSpace: 'nowrap',
                fontFamily: 'monospace',
                textDecoration: 'none',
                display: 'inline-block',
              }}
            >
              View Issue
            </Link>
          </Container>

          <Text style={{ ...text, marginBottom: '24px', marginTop: '32px' }}>
            Health status updates help track the overall condition and progress
            of issues, providing visibility into potential blockers or risks.
          </Text>

          <Text style={{ ...text, marginBottom: '24px' }}>
            You can reply to this message with any questions or{' '}
            <Link
              href='https://www.thehuefactory.co/contact'
              target='_blank'
              style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            >
              contact us
            </Link>{' '}
            for support.
          </Text>
        </Container>

        {/* Footer Section */}
        <Container
          style={{
            ...container,
            marginTop: '48px',
          }}
        >
          <Img
            src={`${baseUrl}/Logo_3dicon_orange.png`}
            width='42'
            height='42'
            alt="thehuefactory's Logo"
          />
          <Text style={{ ...footer, marginTop: '40px' }}>
            <Link
              href='https://www.thehuefactory.co/'
              target='_blank'
              style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            >
              thehuefactory.co
            </Link>{' '}
            <br />
            The Creative Powerhouse.
            <br />
            Copyright © 2024 thehuefactory. All rights reserved.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default IssueHealthUpdate;

// Styles following existing pattern
const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const issueCard = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  padding: '24px',
  marginBottom: '24px',
};

const issueTitle = {
  color: colors[100],
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const detailsContainer = {
  display: 'flex',
  marginBottom: '8px',
  alignItems: 'center',
};

const detailLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
  minWidth: '120px',
};

const detailValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '0',
};

const healthChangeContainer = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '24px',
  padding: '16px',
  backgroundColor: '#fff',
  border: '2px solid #e9ecef',
  borderRadius: '8px',
};

const healthItem = {
  textAlign: 'center' as const,
  flex: '1',
};

const healthLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  fontWeight: 'bold',
  margin: '0 0 8px 0',
  textTransform: 'uppercase' as const,
};

const healthBadge = {
  display: 'inline-block',
  padding: '8px 16px',
  borderRadius: '20px',
  color: 'white',
  fontWeight: 'bold',
  fontSize: '14px',
};

const healthText = {
  color: 'white',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
};

const healthArrow = {
  color: colors[100],
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '0 16px',
};
