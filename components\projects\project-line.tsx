import { Eye, Folder } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { useProjects } from '@/hooks/use-db';
import { DatePicker } from './date-picker';
import { HealthPopover } from './health-popover';
import { LeadSelector } from './lead-selector';
import { PrioritySelector } from './priority-selector';
import { StatusWithPercent } from './status-with-percent';

interface ProjectData {
  id: string;
  name: string;
  description?: string | null;
  target_date?: string | null;
  created_at: string;
  updated_at: string;
  status?: {
    id: string;
    name: string;
    color: string;
    icon_name: string | null;
  } | null;
  priority?: {
    id: string;
    name: string;
    icon_name: string | null;
    sort_order: number | null;
  } | null;
  lead?: {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url?: string | null;
  } | null;
  health?: {
    id: string;
    name: string;
    color: string;
    description?: string | null;
  } | null;
  client?: {
    id: string;
    name: string;
    email: string | null;
    company_name: string | null;
  } | null;
}

interface ProjectLineProps {
  project: ProjectData;
}

// Helper functions to convert database types to component types
const convertPriority = (priority: ProjectData['priority']) => {
  if (!priority) return null;
  return {
    id: priority.id,
    name: priority.name,
    icon_name: priority.icon_name || '',
    sort_order: priority.sort_order || 0,
  };
};

const convertLead = (lead: ProjectData['lead']) => {
  if (!lead) return null;
  return {
    id: lead.id,
    name: lead.full_name || lead.email,
    email: lead.email,
    avatar_url: lead.avatar_url,
  };
};

const convertHealth = (project: ProjectData) => {
  return {
    id: project.id,
    name: project.name,
    description: project.description,
    health: project.health
      ? {
          id: project.health.id,
          name: project.health.name,
          description: project.health.description || undefined,
        }
      : undefined,
    lead: project.lead
      ? {
          id: project.lead.id,
          name: project.lead.full_name || project.lead.email,
          avatar_url: project.lead.avatar_url || undefined,
        }
      : undefined,
  };
};

const convertStatus = (status: ProjectData['status']) => {
  if (!status) return null;
  return {
    id: status.id,
    name: status.name,
    color: status.color || '#6b7280',
    sort_order: 0,
  };
};

export const getPercentComplete = (status: ProjectData['status']) => {
  if (!status) return 0;
  switch (status.name?.toLowerCase()) {
    case 'completed':
    case 'done':
      return 100;
    case 'in progress':
    case 'active':
      return 60;
    case 'planning':
    case 'todo':
      return 20;
    case 'paused':
    case 'on hold':
      return 30;
    case 'cancelled':
    case 'canceled':
      return 0;
    default:
      return 0;
  }
};

export default function ProjectLine({ project }: ProjectLineProps) {
  const router = useRouter();
  const { updateProject } = useProjects();

  // Update handlers with toast notifications
  const handlePriorityChange = (priorityId: string) => {
    updateProject(project.id, { priority_id: priorityId })
      .then(() => {
        toast.success('Priority updated successfully');
      })
      .catch((error) => {
        toast.error('Failed to update priority');
        console.error('Error updating priority:', error);
      });
  };

  const handleLeadChange = (leadId: string) => {
    updateProject(project.id, { lead_id: leadId })
      .then(() => {
        toast.success('Lead updated successfully');
      })
      .catch((error) => {
        toast.error('Failed to update lead');
        console.error('Error updating lead:', error);
      });
  };

  const handleDateChange = (date: Date | undefined) => {
    const targetDate = date ? date.toISOString().split('T')[0] : null;
    updateProject(project.id, { target_date: targetDate })
      .then(() => {
        toast.success('Target date updated successfully');
      })
      .catch((error) => {
        toast.error('Failed to update target date');
        console.error('Error updating target date:', error);
      });
  };

  const handleStatusChange = (statusId: string) => {
    updateProject(project.id, { status_id: statusId })
      .then(() => {
        toast.success('Status updated successfully');
      })
      .catch((error) => {
        toast.error('Failed to update status');
        console.error('Error updating status:', error);
      });
  };

  const handleHealthChange = (healthId: string) => {
    const validHealthId = healthId as
      | 'no-update'
      | 'off-track'
      | 'on-track'
      | 'at-risk';
    updateProject(project.id, { health_id: validHealthId })
      .then(() => {
        toast.success('Health status updated successfully');
      })
      .catch((error) => {
        toast.error('Failed to update health status');
        console.error('Error updating health status:', error);
      });
  };

  const handleViewProject = () => {
    router.push(`/admin/projects/${project.id}`);
  };

  return (
    <div className='w-full flex items-center py-3 px-6 border-b hover:bg-sidebar/50 border-muted-foreground/5 text-sm'>
      {/* Title - Match header width: w-[55%] sm:w-[65%] xl:w-[41%] */}
      <div className='w-[55%] sm:w-[65%] xl:w-[41%] flex items-center gap-2'>
        <div className='relative'>
          <div className='inline-flex size-6 bg-muted/50 items-center justify-center rounded shrink-0'>
            <Folder className='size-4' />
          </div>
        </div>
        <div className='flex flex-col items-start overflow-hidden'>
          <Link
            href={`/admin/projects/${project.id}`}
            className='font-medium truncate w-full'
          >
            {project.name}
          </Link>
        </div>
      </div>

      {/* Health - Match header width: w-[20%] sm:w-[10%] xl:w-[13%] pl-2.5 */}
      <div className='w-[20%] sm:w-[10%] xl:w-[13%] pl-2.5'>
        <HealthPopover
          project={convertHealth(project)}
          onHealthChange={handleHealthChange}
        />
      </div>

      {/* Priority - Match header width: hidden w-[10%] sm:block pl-2 */}
      <div className='hidden w-[10%] sm:block pl-2'>
        <PrioritySelector
          priority={convertPriority(project.priority)}
          onPriorityChange={handlePriorityChange}
        />
      </div>

      {/* Lead - Match header width: hidden xl:block xl:w-[13%] pl-2 */}
      <div className='hidden xl:block xl:w-[13%] pl-2'>
        <LeadSelector
          lead={convertLead(project.lead)}
          onLeadChange={handleLeadChange}
        />
      </div>

      {/* Client - Match header width: hidden xl:block xl:w-[13%] pl-2 */}
      <div className='hidden xl:block xl:w-[13%] pl-2'>
        {project.client ? (
          <div className='flex flex-col'>
            <span className='font-medium text-sm truncate'>
              {project.client.name}
            </span>
            {project.client.email && (
              <span className='text-xs text-muted-foreground truncate'>
                {project.client.email}
              </span>
            )}
          </div>
        ) : (
          <span className='text-sm text-muted-foreground'>No client</span>
        )}
      </div>

      {/* Target date - Match header width: hidden xl:block xl:w-[13%] pl-2.5 */}
      <div className='hidden xl:block xl:w-[13%] pl-2.5'>
        <DatePicker
          date={project.target_date ? new Date(project.target_date) : undefined}
          onDateChange={handleDateChange}
        />
      </div>

      {/* Status - Match header width: w-[20%] sm:w-[10%] pl-2 */}
      <div className='w-[20%] sm:w-[10%] pl-2'>
        <StatusWithPercent
          status={convertStatus(project.status)}
          percentComplete={getPercentComplete(project.status)}
          onStatusChange={handleStatusChange}
        />
      </div>

      {/* Actions - Match header width: w-[5%] sm:w-[5%] xl:w-[5%] pl-2 */}
      <div className='w-[5%] sm:w-[5%] xl:w-[5%] pl-2'>
        <Button
          variant='ghost'
          size='sm'
          className='h-7 w-7 p-0'
          onClick={handleViewProject}
        >
          <Eye className='h-4 w-4' />
        </Button>
      </div>
    </div>
  );
}
