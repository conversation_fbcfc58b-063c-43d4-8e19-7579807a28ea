'use client';

import { ArrowLeft, Edit, Save, X } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useIssues } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';

interface IssueHeaderProps {
  issue: Issue;
  canEdit?: boolean;
  onIssueUpdated?: (updatedIssue: Issue) => void;
  backUrl?: string;
}

export function IssueHeader({ 
  issue, 
  canEdit = false, 
  onIssueUpdated,
  backUrl 
}: IssueHeaderProps) {
  const router = useRouter();
  const { updateIssue, getIssueById } = useIssues();
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editTitle, setEditTitle] = useState(issue.title || '');

  const handleSaveTitle = () => {
    if (!editTitle.trim()) {
      toast.error('Title cannot be empty');
      return;
    }

    setIsUpdating(true);
    updateIssue(issue.id, { title: editTitle.trim() })
      .then(() => {
        toast.success('Issue title updated successfully');
        setIsEditing(false);
        // Refresh issue data
        return getIssueById(issue.id);
      })
      .then((updatedIssue) => {
        if (updatedIssue && onIssueUpdated) {
          onIssueUpdated(updatedIssue);
        }
      })
      .catch((error) => {
        console.error('Error updating issue title:', error);
        toast.error('Failed to update issue title');
      })
      .finally(() => {
        setIsUpdating(false);
      });
  };

  const handleCancelEdit = () => {
    setEditTitle(issue.title || '');
    setIsEditing(false);
  };

  const handleBack = () => {
    if (backUrl) {
      router.push(backUrl);
    } else {
      router.back();
    }
  };

  const getStatusColor = (status: any) => {
    if (!status) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    
    const color = status.color || '#6b7280';
    return `bg-[${color}]/10 text-[${color}] border-[${color}]/20`;
  };

  const getPriorityColor = (priority: any) => {
    if (!priority) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    
    switch (priority.name?.toLowerCase()) {
      case 'urgent':
      case 'high':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
      <div className='flex items-center gap-4 flex-1 min-w-0'>
        <Button
          variant='ghost'
          size='sm'
          onClick={handleBack}
          className='h-8 w-8 p-0 flex-shrink-0'
        >
          <ArrowLeft className='h-4 w-4' />
        </Button>
        
        <div className='flex items-center gap-2 min-w-0 flex-1'>
          {isEditing ? (
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className='h-8 text-sm font-medium'
              placeholder='Issue title'
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSaveTitle();
                } else if (e.key === 'Escape') {
                  handleCancelEdit();
                }
              }}
              autoFocus
            />
          ) : (
            <>
              <p className='font-medium text-sm truncate'>
                {issue.identifier}
              </p>
              {issue.title && (
                <>
                  <span className='text-muted-foreground'>•</span>
                  <p className='text-sm truncate'>{issue.title}</p>
                </>
              )}
            </>
          )}
        </div>

        {/* Status and Priority Badges */}
        <div className='flex items-center gap-2 flex-shrink-0'>
          {issue.status && (
            <Badge 
              variant='outline' 
              className={`text-xs ${getStatusColor(issue.status)}`}
            >
              {issue.status.name}
            </Badge>
          )}
          
          {issue.priority && (
            <Badge 
              variant='outline' 
              className={`text-xs ${getPriorityColor(issue.priority)}`}
            >
              {issue.priority.name}
            </Badge>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className='flex items-center gap-2 flex-shrink-0'>
        {canEdit && (
          <>
            {isEditing ? (
              <>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={handleCancelEdit}
                  disabled={isUpdating}
                  className='h-8'
                >
                  <X className='h-4 w-4' />
                  Cancel
                </Button>
                <Button
                  size='sm'
                  onClick={handleSaveTitle}
                  disabled={isUpdating}
                  className='h-8'
                >
                  <Save className='h-4 w-4' />
                  {isUpdating ? 'Saving...' : 'Save'}
                </Button>
              </>
            ) : (
              <Button
                variant='ghost'
                size='sm'
                onClick={() => setIsEditing(true)}
                className='h-8'
              >
                <Edit className='h-4 w-4' />
                Edit
              </Button>
            )}
          </>
        )}
      </div>
    </header>
  );
}
