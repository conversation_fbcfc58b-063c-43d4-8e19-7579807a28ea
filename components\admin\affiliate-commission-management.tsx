'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  TrendingUp,
  User,
  Users,
} from 'lucide-react';
import { useMemo, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  useBudgets,
  useClients,
  useMembers,
  useProjects,
} from '@/hooks/use-db';
import { cn } from '@/lib/utils/cn';
import { formatCurrency } from '@/lib/utils/format-currency';

interface AffiliateCommissionManagementProps {
  className?: string;
}

export function AffiliateCommissionManagement({
  className,
}: AffiliateCommissionManagementProps) {
  const { budgets } = useBudgets();
  const { members } = useMembers();
  const { projects } = useProjects();
  const { clients } = useClients();

  const [searchTerm, setSearchTerm] = useState('');
  const [affiliateFilter, setAffiliateFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Get affiliates
  const affiliates = useMemo(
    () => members.filter((member) => member.role === 'Affiliate'),
    [members]
  );

  // Get affiliate commissions with enhanced data
  const affiliateCommissions = useMemo(() => {
    return budgets
      .filter((budget) => budget.has_affiliate && budget.affiliateId)
      .map((budget) => {
        const affiliate = affiliates.find((a) => a.id === budget.affiliateId);
        const project = projects.find((p) => p.id === budget.ProjectId);
        const client = clients.find((c) => c.id === budget.ClientId);

        return {
          ...budget,
          affiliate,
          project,
          client,
        };
      });
  }, [budgets, affiliates, projects, clients]);

  // Filter commissions
  const filteredCommissions = useMemo(() => {
    let filtered = affiliateCommissions;

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (commission) =>
          commission.affiliate?.full_name?.toLowerCase().includes(term) ||
          commission.project?.name.toLowerCase().includes(term) ||
          commission.client?.name.toLowerCase().includes(term)
      );
    }

    // Filter by affiliate
    if (affiliateFilter !== 'all') {
      filtered = filtered.filter(
        (commission) => commission.affiliateId === affiliateFilter
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(
        (commission) => commission.Status === statusFilter
      );
    }

    return filtered.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [affiliateCommissions, searchTerm, affiliateFilter, statusFilter]);

  // Calculate analytics
  const analytics = useMemo(() => {
    const totalCommissions = affiliateCommissions.reduce(
      (sum, commission) => sum + (commission.AffiliateCommission || 0),
      0
    );

    const pendingCommissions = affiliateCommissions
      .filter((c) => c.Status === 'approved' || c.Status === 'locked')
      .reduce(
        (sum, commission) => sum + (commission.AffiliateCommission || 0),
        0
      );

    const paidCommissions = affiliateCommissions
      .filter((c) => c.Status === 'spent')
      .reduce(
        (sum, commission) => sum + (commission.AffiliateCommission || 0),
        0
      );

    const activeAffiliates = new Set(
      affiliateCommissions.map((c) => c.affiliateId)
    ).size;

    // Top performing affiliates
    const affiliatePerformance = affiliates
      .map((affiliate) => {
        const affiliateCommissionData = affiliateCommissions.filter(
          (c) => c.affiliateId === affiliate.id
        );
        const totalEarned = affiliateCommissionData.reduce(
          (sum, c) => sum + (c.AffiliateCommission || 0),
          0
        );
        const projectCount = affiliateCommissionData.length;

        return {
          affiliate,
          totalEarned,
          projectCount,
          commissions: affiliateCommissionData,
        };
      })
      .sort((a, b) => b.totalEarned - a.totalEarned);

    return {
      totalCommissions,
      pendingCommissions,
      paidCommissions,
      activeAffiliates,
      totalAffiliates: affiliates.length,
      topPerformers: affiliatePerformance.slice(0, 5),
    };
  }, [affiliateCommissions, affiliates]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'locked':
        return 'bg-yellow-100 text-yellow-800';
      case 'spent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className='h-4 w-4' />;
      case 'locked':
        return <AlertTriangle className='h-4 w-4' />;
      case 'spent':
        return <CheckCircle className='h-4 w-4' />;
      default:
        return <Clock className='h-4 w-4' />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Analytics Overview */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Commissions
            </CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.totalCommissions, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Across all affiliates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Pending Payouts
            </CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.pendingCommissions, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>
              Awaiting project completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Active Affiliates
            </CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {analytics.activeAffiliates}
            </div>
            <p className='text-xs text-muted-foreground'>
              Of {analytics.totalAffiliates} total affiliates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Paid Commissions
            </CardTitle>
            <CheckCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {formatCurrency(analytics.paidCommissions, 'USD')}
            </div>
            <p className='text-xs text-muted-foreground'>Completed payouts</p>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers & Commission Management */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        <Card className='lg:col-span-1'>
          <CardHeader>
            <CardTitle>Top Performers</CardTitle>
            <CardDescription>Highest earning affiliates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {analytics.topPerformers.map((performer, index) => (
                <div
                  key={performer.affiliate.id}
                  className='flex items-center justify-between'
                >
                  <div className='flex items-center gap-3'>
                    <div className='w-8 h-8 rounded-full bg-muted flex items-center justify-center text-sm font-medium'>
                      {index + 1}
                    </div>
                    <div>
                      <div className='font-medium'>
                        {performer.affiliate.full_name}
                      </div>
                      <div className='text-sm text-muted-foreground'>
                        {performer.projectCount} projects
                      </div>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='font-medium'>
                      {formatCurrency(performer.totalEarned, 'USD')}
                    </div>
                  </div>
                </div>
              ))}
              {analytics.topPerformers.length === 0 && (
                <div className='text-center py-4 text-muted-foreground'>
                  <TrendingUp className='h-8 w-8 mx-auto mb-2 opacity-50' />
                  <p className='text-sm'>No affiliate activity yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className='lg:col-span-2'>
          <CardHeader>
            <CardTitle>Commission Management</CardTitle>
            <CardDescription>
              Manage affiliate commissions and payouts
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className='flex flex-col sm:flex-row gap-4 mb-6'>
              <div className='flex-1'>
                <Input
                  placeholder='Search commissions...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className='flex gap-2'>
                <Select
                  value={affiliateFilter}
                  onValueChange={setAffiliateFilter}
                >
                  <SelectTrigger className='w-[140px]'>
                    <SelectValue placeholder='Affiliate' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>All Affiliates</SelectItem>
                    {affiliates.map((affiliate) => (
                      <SelectItem key={affiliate.id} value={affiliate.id}>
                        {affiliate.full_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className='w-[120px]'>
                    <SelectValue placeholder='Status' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='all'>All Status</SelectItem>
                    <SelectItem value='draft'>Draft</SelectItem>
                    <SelectItem value='approved'>Approved</SelectItem>
                    <SelectItem value='locked'>Locked</SelectItem>
                    <SelectItem value='spent'>Spent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Commission Table */}
            {filteredCommissions.length > 0 ? (
              <div className='border rounded-lg max-h-[400px] overflow-y-auto'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Affiliate</TableHead>
                      <TableHead>Project</TableHead>
                      <TableHead>Commission</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className='w-[80px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCommissions.slice(0, 10).map((commission) => (
                      <TableRow key={commission.id}>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            <User className='h-4 w-4 text-muted-foreground' />
                            <div>
                              <div className='font-medium'>
                                {commission.affiliate?.full_name || 'Unknown'}
                              </div>
                              <div className='text-sm text-muted-foreground'>
                                {commission.affiliate?.email}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className='font-medium'>
                              {commission.project?.name || 'Unknown Project'}
                            </div>
                            <div className='text-sm text-muted-foreground'>
                              {commission.client?.name || 'Unknown Client'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='font-medium text-green-600'>
                            {formatCurrency(
                              commission.AffiliateCommission || 0,
                              commission.Currency
                            )}
                          </div>
                          <div className='text-sm text-muted-foreground'>
                            {commission.AffiliateCommission
                              ? (
                                  (commission.AffiliateCommission /
                                    commission.ActualAmount) *
                                  100
                                ).toFixed(1)
                              : 0}
                            % rate
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='flex items-center gap-2'>
                            {getStatusIcon(commission.Status)}
                            <Badge
                              className={getStatusColor(commission.Status)}
                            >
                              {commission.Status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='text-sm'>
                            {formatDate(commission.created_at)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            size='sm'
                            variant='ghost'
                            className='h-8 w-8 p-0'
                          >
                            <Eye className='h-4 w-4' />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className='text-center py-8 text-muted-foreground'>
                <DollarSign className='h-12 w-12 mx-auto mb-4 opacity-50' />
                <p>No commissions found</p>
                <p className='text-sm'>
                  {searchTerm ||
                  affiliateFilter !== 'all' ||
                  statusFilter !== 'all'
                    ? 'Try adjusting your filters'
                    : 'No affiliate commissions yet'}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
