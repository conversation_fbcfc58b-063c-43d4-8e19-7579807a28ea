'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import {
  useBudgets,
  useClients,
  useProjects,
  useProposals,
} from '@/hooks/use-db';
import { ProposalProcessingService } from '@/lib/services/proposal-processing-service';
import type { Proposal } from '@/lib/supabase/database-modules';

/**
 * Test component for proposal workflow integration
 * This component allows testing the complete proposal-to-client-budget workflow
 */
export function ProposalWorkflowTest() {
  const { proposals } = useProposals();
  const { addClient, fetchClient } = useClients();
  const { addBudget } = useBudgets();
  const { addProject } = useProjects();

  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(
    null
  );
  const [testResults, setTestResults] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get pending proposals for testing
  const pendingProposals = proposals.filter((p) => p.is_approved === null);

  const handleTestProposal = async (proposal: Proposal) => {
    setIsProcessing(true);
    setSelectedProposal(proposal);

    try {
      // First validate the proposal
      const validation =
        ProposalProcessingService.validateAndExtractProposalData(proposal);

      let results = `=== Proposal Validation Test ===\n`;
      results += `Proposal ID: ${proposal.id}\n`;
      results += `Client: ${proposal.client_name}\n`;
      results += `Valid: ${validation.isValid}\n\n`;

      if (validation.errors.length > 0) {
        results += `Errors:\n${validation.errors.map((e) => `❌ ${e}`).join('\n')}\n\n`;
      }

      if (validation.warnings.length > 0) {
        results += `Warnings:\n${validation.warnings.map((w) => `⚠️ ${w}`).join('\n')}\n\n`;
      }

      results += `Extracted Data:\n`;
      results += `Client: ${JSON.stringify(validation.extractedData.clientData, null, 2)}\n`;
      results += `Project: ${JSON.stringify(validation.extractedData.projectData, null, 2)}\n`;
      results += `Budget: ${JSON.stringify(validation.extractedData.budgetData, null, 2)}\n\n`;

      if (validation.isValid) {
        // Test the processing workflow
        results += `=== Processing Test ===\n`;

        const processingResult =
          await ProposalProcessingService.processApprovedProposal(proposal, {
            addClient,
            fetchClient,
            addProject,
            addBudget,
          });

        const report = ProposalProcessingService.generateProcessingReport(
          proposal,
          processingResult
        );
        results += report;

        if (processingResult.success) {
          toast.success('Proposal processing test completed successfully!');
        } else {
          toast.error('Proposal processing test failed');
        }
      } else {
        results += `Processing skipped due to validation errors.\n`;
        toast.warning('Proposal validation failed - processing skipped');
      }

      setTestResults(results);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorResults = `=== Test Error ===\n${errorMessage}\n\n${testResults}`;
      setTestResults(errorResults);
      toast.error(`Test failed: ${errorMessage}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClearResults = () => {
    setTestResults('');
    setSelectedProposal(null);
  };

  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle>Proposal Workflow Test</CardTitle>
          <CardDescription>
            Test the complete proposal-to-client-budget integration workflow.
            This will validate proposal data and simulate the processing without
            affecting live data.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div>
            <h4 className='text-sm font-medium mb-2'>
              Pending Proposals ({pendingProposals.length})
            </h4>
            {pendingProposals.length === 0 ? (
              <p className='text-sm text-muted-foreground'>
                No pending proposals available for testing.
              </p>
            ) : (
              <div className='grid gap-2'>
                {pendingProposals.slice(0, 5).map((proposal) => (
                  <div
                    key={proposal.id}
                    className='flex items-center justify-between p-3 border rounded-lg'
                  >
                    <div>
                      <p className='font-medium'>
                        {proposal.client_name || 'Unknown Client'}
                      </p>
                      <p className='text-sm text-muted-foreground'>
                        ID: {proposal.id} | Affiliate: {proposal.user_email}
                      </p>
                    </div>
                    <Button
                      size='sm'
                      onClick={() => handleTestProposal(proposal)}
                      disabled={isProcessing}
                    >
                      {isProcessing && selectedProposal?.id === proposal.id
                        ? 'Testing...'
                        : 'Test'}
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {testResults && (
            <div className='space-y-2'>
              <div className='flex items-center justify-between'>
                <h4 className='text-sm font-medium'>Test Results</h4>
                <Button
                  size='sm'
                  variant='outline'
                  onClick={handleClearResults}
                >
                  Clear
                </Button>
              </div>
              <Textarea
                value={testResults}
                readOnly
                className='min-h-[400px] font-mono text-xs'
                placeholder='Test results will appear here...'
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Workflow Integration Status</CardTitle>
          <CardDescription>
            Current status of the proposal-to-client-budget integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid gap-3'>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>✅ Proposal validation system</span>
            </div>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>
                ✅ Client creation/lookup workflow
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>
                ✅ Project creation from proposals
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>
                ✅ Budget creation with affiliate commission
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>
                ✅ Automatic processing on approval
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>✅ Error handling and rollback</span>
            </div>
            <div className='flex items-center gap-2'>
              <div className='w-3 h-3 bg-green-500 rounded-full'></div>
              <span className='text-sm'>
                ✅ Comprehensive logging and reporting
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Integration Points</CardTitle>
          <CardDescription>
            Key integration points in the workflow
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-3 text-sm'>
            <div>
              <strong>Proposal Approval:</strong>
              <span className='ml-2 text-muted-foreground'>
                components/tables/proposals/proposals-table.tsx -
                handleApprovalChange()
              </span>
            </div>
            <div>
              <strong>Processing Service:</strong>
              <span className='ml-2 text-muted-foreground'>
                lib/services/proposal-processing-service.ts
              </span>
            </div>
            <div>
              <strong>Data Hooks:</strong>
              <span className='ml-2 text-muted-foreground'>
                hooks/use-db.ts - useClients, useBudgets, useProjects
              </span>
            </div>
            <div>
              <strong>Workflow Hook:</strong>
              <span className='ml-2 text-muted-foreground'>
                hooks/use-db.ts - useProposalWorkflow
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
