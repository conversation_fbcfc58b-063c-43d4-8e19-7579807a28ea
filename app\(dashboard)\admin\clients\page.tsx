'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ClientDashboard } from '@/components/clients/client-dashboard';
import { ClientList } from '@/components/clients/client-list';

export default function ClientsPage() {
  return (
    <main className="h-full flex flex-col">
      <header className="pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full">
        <p className="font-medium text-sm">Client Management</p>
      </header>
      <section className="flex-1 p-6">
        <Tabs defaultValue="dashboard" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="clients">All Clients</TabsTrigger>
          </TabsList>
          
          <TabsContent value="dashboard" className="space-y-6 mt-6">
            <ClientDashboard />
          </TabsContent>
          
          <TabsContent value="clients" className="space-y-6 mt-6">
            <ClientList />
          </TabsContent>
        </Tabs>
      </section>
    </main>
  );
}
