'use client';

import { Bell, Calendar, CheckCircle, Clock, Plus } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { StatusIcon } from '@/lib/constants/status';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { VolunteerMetrics } from '@/components/volunteer/volunteer-metrics';
import { useIssues, useProjects, useProfile } from '@/hooks/use-db';
import type { Issue, Project } from '@/lib/supabase/database-modules';

export function VolunteerDashboard() {
  const { profile } = useProfile();
  const { getVolunteerIssues } = useIssues();
  const { getVolunteerProjects } = useProjects();

  const [recentIssues, setRecentIssues] = useState<Issue[]>([]);
  const [activeProjects, setActiveProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!profile?.id) return;

    setLoading(true);

    Promise.all([
      getVolunteerIssues(profile.id),
      getVolunteerProjects(profile.id),
    ])
      .then(([issues, projects]) => {
        // Get recent issues (last 5)
        const sortedIssues = issues
          .sort(
            (a, b) =>
              new Date(b.updated_at).getTime() -
              new Date(a.updated_at).getTime()
          )
          .slice(0, 5);
        setRecentIssues(sortedIssues);

        // Get active projects
        const activeProjs = projects.filter(
          (p) =>
            p.status?.name &&
            !['Completed', 'Cancelled', 'On Hold'].includes(p.status.name)
        );
        setActiveProjects(activeProjs);
      })
      .catch(console.error)
      .finally(() => setLoading(false));
  }, [profile?.id, getVolunteerIssues, getVolunteerProjects]);

  const upcomingDeadlines = [
    ...recentIssues
      .filter((i) => i.due_date)
      .map((i) => ({
        id: i.id,
        title: i.title || 'Untitled Issue',
        type: 'issue' as const,
        dueDate: i.due_date!,
        url: `/volunteer/issues/${i.id}`,
      })),
    ...activeProjects
      .filter((p) => p.due_date)
      .map((p) => ({
        id: p.id,
        title: p.name || 'Untitled Project',
        type: 'project' as const,
        dueDate: p.due_date!,
        url: `/volunteer/projects/${p.id}`,
      })),
  ]
    .sort(
      (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
    )
    .slice(0, 5);

  return (
    <div className='space-y-6'>
      {/* Welcome Section */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold'>
            Welcome back, {profile?.full_name || 'Volunteer'}!
          </h1>
          <p className='text-muted-foreground'>
            Here's what's happening with your volunteer work today.
          </p>
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='sm' asChild>
            <Link href='/volunteer/inbox'>
              <Bell className='h-4 w-4 mr-2' />
              Inbox
            </Link>
          </Button>
          <Button size='sm' asChild>
            <Link href='/volunteer/issues'>
              <Plus className='h-4 w-4 mr-2' />
              View Issues
            </Link>
          </Button>
        </div>
      </div>

      {/* Metrics */}
      <VolunteerMetrics />

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Recent Issues */}
        <Card>
          <CardHeader className='flex flex-row items-center justify-between'>
            <CardTitle className='text-base'>Recent Issues</CardTitle>
            <Button variant='ghost' size='sm' asChild>
              <Link href='/volunteer/issues'>View All</Link>
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='space-y-3'>
                {Array.from({ length: 3 }).map((_, i) => (
                  <div
                    key={i}
                    className='flex items-center gap-3 p-3 border rounded-lg animate-pulse'
                  >
                    <div className='w-8 h-8 bg-muted rounded' />
                    <div className='flex-1 space-y-1'>
                      <div className='h-4 bg-muted rounded w-3/4' />
                      <div className='h-3 bg-muted rounded w-1/2' />
                    </div>
                  </div>
                ))}
              </div>
            ) : recentIssues.length > 0 ? (
              <ScrollArea className='max-h-[300px]'>
                <div className='space-y-3'>
                  {recentIssues.map((issue) => (
                    <Link
                      key={issue.id}
                      href={`/volunteer/issues/${issue.id}`}
                      className='flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors'
                    >
                      <div className='flex items-center gap-2'>
                        {issue.status && (
                          <StatusIcon status={issue.status.name} />
                        )}
                      </div>
                      <div className='flex-1 min-w-0'>
                        <p className='font-medium truncate'>
                          {issue.title || 'Untitled Issue'}
                        </p>
                        <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                          <span>{issue.project?.name || 'No project'}</span>
                          {issue.due_date && (
                            <>
                              <span>•</span>
                              <span>
                                Due{' '}
                                {new Date(issue.due_date).toLocaleDateString()}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className='text-center py-8 text-muted-foreground'>
                <CheckCircle className='h-12 w-12 mx-auto mb-3 opacity-50' />
                <p className='text-sm'>No recent issues</p>
                <p className='text-xs'>
                  Issues assigned to you will appear here
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Active Projects */}
        <Card>
          <CardHeader className='flex flex-row items-center justify-between'>
            <CardTitle className='text-base'>Active Projects</CardTitle>
            <Button variant='ghost' size='sm' asChild>
              <Link href='/volunteer/projects'>View All</Link>
            </Button>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className='space-y-3'>
                {Array.from({ length: 3 }).map((_, i) => (
                  <div
                    key={i}
                    className='flex items-center gap-3 p-3 border rounded-lg animate-pulse'
                  >
                    <div className='w-8 h-8 bg-muted rounded' />
                    <div className='flex-1 space-y-1'>
                      <div className='h-4 bg-muted rounded w-3/4' />
                      <div className='h-3 bg-muted rounded w-1/2' />
                    </div>
                  </div>
                ))}
              </div>
            ) : activeProjects.length > 0 ? (
              <ScrollArea className='max-h-[300px]'>
                <div className='space-y-3'>
                  {activeProjects.map((project) => (
                    <Link
                      key={project.id}
                      href={`/volunteer/projects/${project.id}`}
                      className='flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors'
                    >
                      <div className='flex items-center gap-2'>
                        {project.status && (
                          <StatusIcon status={project.status.name} />
                        )}
                      </div>
                      <div className='flex-1 min-w-0'>
                        <p className='font-medium truncate'>
                          {project.name || 'Untitled Project'}
                        </p>
                        <div className='flex items-center gap-2 text-xs text-muted-foreground'>
                          <span>{project.client?.name || 'No client'}</span>
                          {project.due_date && (
                            <>
                              <span>•</span>
                              <span>
                                Due{' '}
                                {new Date(
                                  project.due_date
                                ).toLocaleDateString()}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                      {project.lead && (
                        <Avatar className='h-6 w-6'>
                          <AvatarImage
                            src={project.lead.avatar_url || ''}
                            alt={project.lead.full_name || 'Lead'}
                          />
                          <AvatarFallback>
                            {project.lead.full_name
                              ?.split(' ')
                              .map((n) => n[0])
                              .join('') || 'L'}
                          </AvatarFallback>
                        </Avatar>
                      )}
                    </Link>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className='text-center py-8 text-muted-foreground'>
                <CheckCircle className='h-12 w-12 mx-auto mb-3 opacity-50' />
                <p className='text-sm'>No active projects</p>
                <p className='text-xs'>
                  Projects you're involved in will appear here
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Deadlines */}
      {upcomingDeadlines.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Clock className='h-5 w-5' />
              Upcoming Deadlines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {upcomingDeadlines.map((item) => {
                const dueDate = new Date(item.dueDate);
                const now = new Date();
                const daysUntilDue = Math.ceil(
                  (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
                );
                const isOverdue = daysUntilDue < 0;
                const isUrgent = daysUntilDue <= 3 && daysUntilDue >= 0;

                return (
                  <Link
                    key={`${item.type}-${item.id}`}
                    href={item.url}
                    className='flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors'
                  >
                    <div className='flex items-center gap-3'>
                      <Calendar className='h-4 w-4 text-muted-foreground' />
                      <div>
                        <p className='font-medium'>{item.title}</p>
                        <p className='text-xs text-muted-foreground capitalize'>
                          {item.type} • Due {dueDate.toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div>
                      {isOverdue ? (
                        <Badge variant='destructive' className='text-xs'>
                          Overdue
                        </Badge>
                      ) : isUrgent ? (
                        <Badge
                          variant='outline'
                          className='text-xs text-orange-600 border-orange-600'
                        >
                          Due Soon
                        </Badge>
                      ) : (
                        <Badge variant='secondary' className='text-xs'>
                          {daysUntilDue} days
                        </Badge>
                      )}
                    </div>
                  </Link>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
