import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { TeamRoleChange } from '@/lib/emails/teams/role-changed';
import type { TeamRoleChangeEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve, reject) => {
    let emailData: TeamRoleChangeEmailData;

    request
      .json()
      .then((data: TeamRoleChangeEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.team ||
          !emailData.member ||
          !emailData.oldRole ||
          !emailData.newRole ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: team, member, oldRole, newRole, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = TeamRoleChange(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(TeamRoleChange(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Role Updated in ${emailData.team.name}`,
          react: TeamRoleChange(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Team role change email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Role Updated in ${emailData.team.name}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Team role change email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error: error.message || 'Failed to send team role change email',
          })
        );
      });
  });
}
