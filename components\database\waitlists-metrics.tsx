'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Clock, TrendingUp, User<PERSON>he<PERSON>, Users } from 'lucide-react';
import { useMemo } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import type { Waitlist } from '@/lib/supabase/database-modules';

interface WaitlistsMetricsProps {
  waitlists: Waitlist[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
  loading?: boolean;
}

function MetricCard({
  title,
  value,
  subtitle,
  variant = 'default',
  icon,
  loading = false,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  if (loading) {
    return (
      <Card className={getVariantStyles()}>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            <Skeleton className='h-4 w-24' />
          </CardTitle>
          <Skeleton className='h-4 w-4' />
        </CardHeader>
        <CardContent>
          <Skeleton className='h-8 w-16 mb-1' />
          <Skeleton className='h-3 w-32' />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={getVariantStyles()}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium'>{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>{value}</div>
        <p className='text-xs text-muted-foreground'>{subtitle}</p>
      </CardContent>
    </Card>
  );
}

export function WaitlistsMetrics({
  waitlists,
  loading = false,
}: WaitlistsMetricsProps) {
  const metrics = useMemo(() => {
    if (loading) {
      return Array(6)
        .fill(null)
        .map(() => ({
          title: '',
          value: '',
          subtitle: '',
          icon: null,
          variant: 'default' as const,
          loading: true,
        }));
    }

    // Calculate email status metrics
    const emailsSent = waitlists.filter(
      (w) => w.is_ld_email_sent === true
    ).length;
    const emailsPending = waitlists.filter(
      (w) => w.is_ld_email_sent === false || w.is_ld_email_sent === null
    ).length;

    // Calculate time-based metrics
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const recentWaitlists = waitlists.filter(
      (w) => new Date(w.created_at) > sevenDaysAgo
    ).length;

    const monthlyWaitlists = waitlists.filter(
      (w) => new Date(w.created_at) > thirtyDaysAgo
    ).length;

    // Calculate email completion rate
    const emailCompletionRate =
      waitlists.length > 0 ? (emailsSent / waitlists.length) * 100 : 0;

    return [
      {
        title: 'Total Waitlists',
        value: waitlists.length,
        subtitle: `${monthlyWaitlists} this month`,
        icon: <Users className='h-4 w-4 text-blue-600' />,
        variant: 'default' as const,
        loading: false,
      },
      {
        title: 'Emails Pending',
        value: emailsPending,
        subtitle: 'Awaiting email send',
        icon: <Clock className='h-4 w-4 text-yellow-600' />,
        variant: emailsPending > 10 ? 'warning' : ('default' as const),
        loading: false,
      },
      {
        title: 'Emails Sent',
        value: emailsSent,
        subtitle: 'Successfully sent',
        icon: <CheckCircle className='h-4 w-4 text-green-600' />,
        variant: 'success' as const,
        loading: false,
      },
      {
        title: 'This Week',
        value: recentWaitlists,
        subtitle: 'New signups',
        icon: <TrendingUp className='h-4 w-4 text-purple-600' />,
        variant: recentWaitlists > 0 ? 'success' : ('default' as const),
        loading: false,
      },
      {
        title: 'Email Rate',
        value: `${emailCompletionRate.toFixed(1)}%`,
        subtitle: `${emailsSent} sent`,
        icon: <UserCheck className='h-4 w-4 text-indigo-600' />,
        variant:
          emailCompletionRate > 70
            ? 'success'
            : emailCompletionRate > 40
              ? 'warning'
              : ('destructive' as const),
        loading: false,
      },
      {
        title: 'Active Entries',
        value: waitlists.filter((w) => w.full_name && w.full_name.trim() !== '')
          .length,
        subtitle: 'With valid names',
        icon: <Users className='h-4 w-4 text-indigo-600' />,
        variant: 'default' as const,
        loading: false,
      },
    ];
  }, [waitlists, loading]);

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-lg font-semibold'>Waitlists Overview</h2>
        <p className='text-sm text-muted-foreground'>
          Statistics and insights for application submissions and waitlist
          management
        </p>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4'>
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            subtitle={metric.subtitle}
            variant={
              (metric.variant as
                | 'default'
                | 'success'
                | 'warning'
                | 'destructive') || 'default'
            }
            icon={metric.icon}
            loading={metric.loading}
          />
        ))}
      </div>
    </div>
  );
}
