import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { AcceptedApplication } from '@/lib/emails/collaborators';
import type { Database } from '@/lib/supabase/database-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

type dataProps = {
  approved: Database['public']['Enums']['is_accepted'];
  created_at: string;
  email: string | null;
  full_name: string | null;
  id: number;
  join_role: Database['public']['Enums']['Role Types'];
  location: string | null;
  message: string | null;
  phone: string | null;
  position: string | null;
  position_other: string | null;
  referer: string | null;
  resume_url: string | null;
  reviewed: Database['public']['Enums']['is_reviewed'];
  user_id: string;
  pass: string;
};

export async function POST(request: Request) {
  const form: dataProps = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: form.email ? [form.email] : [],
      bcc: ['<EMAIL>'],
      subject: 'Your Application Has Been Accepted.',
      text: '',
      react: AcceptedApplication(form),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
