export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number; // in milliseconds, 0 means persistent
  timestamp: string;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

export type NotificationCallback = (notifications: Notification[]) => void;

// Internal state for the notification service
const notifications: Notification[] = [];
const callbacks = new Set<NotificationCallback>();
let nextId = 1;

const notifyCallbacks = (): void => {
  const currentNotifications = [...notifications];
  callbacks.forEach((callback) => {
    try {
      callback(currentNotifications);
    } catch (error) {
      console.error('Error in notification callback:', error);
    }
  });
};

export const notificationService = {
  /**
   * Subscribe to notification changes
   */
  subscribe: (callback: NotificationCallback): (() => void) => {
    callbacks.add(callback);
    // Immediately call with current notifications
    callback([...notifications]);

    // Return unsubscribe function
    return () => {
      callbacks.delete(callback);
    };
  },

  /**
   * Add a new notification
   */
  add: (notification: Omit<Notification, 'id' | 'timestamp'>): string => {
    const id = `notification-${nextId++}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date().toISOString(),
      duration: notification.duration ?? 5000, // Default 5 seconds
    };

    notifications.push(newNotification);
    notifyCallbacks();

    // Auto-remove after duration (if not persistent)
    if (newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        notificationService.remove(id);
      }, newNotification.duration);
    }

    return id;
  },

  /**
   * Remove a notification by ID
   */
  remove: (id: string): void => {
    const index = notifications.findIndex((n) => n.id === id);
    if (index !== -1) {
      notifications.splice(index, 1);
      notifyCallbacks();
    }
  },

  /**
   * Clear all notifications
   */
  clear: (): void => {
    notifications.length = 0;
    notifyCallbacks();
  },

  /**
   * Get all current notifications
   */
  getAll: (): Notification[] => {
    return [...notifications];
  },

  /**
   * Convenience methods for different notification types
   */
  success: (title: string, message?: string, duration?: number): string => {
    return notificationService.add({
      type: 'success',
      title,
      message,
      duration,
    });
  },

  error: (title: string, message?: string, duration?: number): string => {
    return notificationService.add({
      type: 'error',
      title,
      message,
      duration: duration ?? 0,
    }); // Errors are persistent by default
  },

  warning: (title: string, message?: string, duration?: number): string => {
    return notificationService.add({
      type: 'warning',
      title,
      message,
      duration,
    });
  },

  info: (title: string, message?: string, duration?: number): string => {
    return notificationService.add({ type: 'info', title, message, duration });
  },

  /**
   * Issue-specific notification helpers
   */
  issueCreated: (issueTitle: string): string => {
    return notificationService.success(
      'Issue Created',
      `"${issueTitle}" has been created successfully`
    );
  },

  issueUpdated: (issueTitle: string): string => {
    return notificationService.success(
      'Issue Updated',
      `"${issueTitle}" has been updated`
    );
  },

  issueDeleted: (issueTitle: string): string => {
    return notificationService.success(
      'Issue Deleted',
      `"${issueTitle}" has been deleted`
    );
  },

  issueAssigned: (issueTitle: string, assigneeName: string): string => {
    return notificationService.info(
      'Issue Assigned',
      `"${issueTitle}" has been assigned to ${assigneeName}`
    );
  },

  issueStatusChanged: (issueTitle: string, newStatus: string): string => {
    return notificationService.info(
      'Status Changed',
      `"${issueTitle}" status changed to ${newStatus}`
    );
  },

  /**
   * Project-specific notification helpers
   */
  projectMemberAdded: (memberName: string, projectName: string): string => {
    return notificationService.success(
      'Member Added',
      `${memberName} has been added to "${projectName}"`
    );
  },

  projectCreated: (projectName: string): string => {
    return notificationService.success(
      'Project Created',
      `"${projectName}" has been created successfully`
    );
  },

  projectUpdated: (projectName: string): string => {
    return notificationService.success(
      'Project Updated',
      `"${projectName}" has been updated`
    );
  },
};
