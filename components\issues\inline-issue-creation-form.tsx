'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { FileText, Plus, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { DueDateSelector } from '@/components/layouts/sidebar/create-new-issue/due-date-selector';
import { LabelSelector } from '@/components/layouts/sidebar/create-new-issue/label-selector';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useIssues, usePriorities, useStatus } from '@/hooks/use-db';
import { PriorityIcon } from '@/lib/constants/priorities';
import { StatusIcon } from '@/lib/constants/status';
import type { CreateIssueInput } from '@/lib/supabase/database-modules';
import { extractErrorMessage } from '@/lib/utils/error-utils';
import { IssueRankUtils } from '@/lib/utils/lexorank-utils';

// Zod schema for form validation
const createIssueSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title is too long'),
  description: z.string().optional(),
  status_id: z.string().min(1, 'Status is required'),
  priority_id: z.string().min(1, 'Priority is required'),
  labels: z.array(z.string()).optional().default([]),
  due_date: z.string().optional(),
});

type CreateIssueFormData = z.infer<typeof createIssueSchema>;

interface InlineIssueCreationFormProps {
  projectId: string;
  projectName: string;
  currentUserId?: string;
  projectMembers?: Array<{ user_id: string; role?: string }>;
  onIssueCreated?: () => void;
  className?: string;
}

export function InlineIssueCreationForm({
  projectId,
  projectName,
  currentUserId,
  projectMembers = [],
  onIssueCreated,
  className,
}: InlineIssueCreationFormProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addIssue, issues } = useIssues();
  const { status } = useStatus();
  const { priorities } = usePriorities();

  // Find the current user in project members to get the correct assignee
  const currentProjectMember = projectMembers.find(
    (member) => member.user_id === currentUserId
  );
  const defaultAssigneeId = currentProjectMember?.user_id || currentUserId;

  const form = useForm({
    resolver: zodResolver(createIssueSchema),
    defaultValues: {
      title: '',
      description: '',
      status_id: '',
      priority_id: '',
      labels: [] as string[],
      due_date: '',
    },
  });

  const onSubmit = async (data: CreateIssueFormData) => {
    if (isSubmitting) return;

    setIsSubmitting(true);

    try {
      // Generate rank for new issue (add to top of status column)
      const existingIssuesInStatus = issues.filter(
        (issue) => issue.status_id === data.status_id
      );
      const rank = IssueRankUtils.getTopRank(existingIssuesInStatus);

      const issueInput: CreateIssueInput = {
        title: data.title.trim(),
        description: data.description?.trim() || undefined,
        status_id: data.status_id,
        priority_id: data.priority_id,
        project_id: projectId,
        assignee_id: defaultAssigneeId || undefined,
        rank,
        labels: data.labels || [],
        due_date: data.due_date || undefined,
      };

      await addIssue(issueInput);

      toast.success('Issue created successfully');

      // Reset form and collapse
      form.reset();
      setIsExpanded(false);

      // Notify parent component
      onIssueCreated?.();
    } catch (error) {
      console.error('Failed to create issue:', error);
      const errorMessage = extractErrorMessage(
        error,
        'Failed to create issue. Please try again.'
      );
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    setIsExpanded(false);
  };

  const handleExpand = () => {
    setIsExpanded(true);
    // Set default values when expanding
    if (status.length > 0 && !form.getValues('status_id')) {
      // Try to find "To Do", "Todo", "Backlog", or similar status, otherwise use first
      const todoStatus = status.find((s) =>
        ['to do', 'todo', 'backlog', 'new', 'open'].includes(
          s.name.toLowerCase()
        )
      );
      const defaultStatus = todoStatus || status[0];
      form.setValue('status_id', defaultStatus.id);
    }
    if (priorities.length > 0 && !form.getValues('priority_id')) {
      // Set medium priority as default, or first priority if medium not found
      const mediumPriority = priorities.find((p) =>
        p.name.toLowerCase().includes('medium')
      );
      const defaultPriority = mediumPriority || priorities[0];
      form.setValue('priority_id', defaultPriority.id);
    }
  };

  if (!isExpanded) {
    return (
      <div className={className}>
        <Button
          onClick={handleExpand}
          variant='outline'
          className='w-full justify-start text-muted-foreground hover:text-foreground'
          size='sm'
        >
          <Plus className='h-4 w-4 mr-2' />
          Create new issue in {projectName}
        </Button>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className='pb-3'>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center gap-2 text-base'>
            <FileText className='h-4 w-4' />
            Create New Issue
          </CardTitle>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            <X className='h-4 w-4' />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter issue title'
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Describe the issue...'
                      className='min-h-[80px]'
                      disabled={isSubmitting}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='status_id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select status' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {status.map((s) => (
                          <SelectItem key={s.id} value={s.id}>
                            <div className='flex items-center gap-2'>
                              <StatusIcon statusId={s.id} />
                              {s.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='priority_id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select priority' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {priorities.map((p) => (
                          <SelectItem key={p.id} value={p.id}>
                            <div className='flex items-center gap-2'>
                              <PriorityIcon PriorityName={p.id} />
                              {p.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Labels and Due Date Fields */}
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='labels'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Labels</FormLabel>
                    <FormControl>
                      <LabelSelector
                        value={field.value || []}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='due_date'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Due Date</FormLabel>
                    <FormControl>
                      <DueDateSelector
                        value={field.value || ''}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='flex items-center justify-end gap-2 pt-2'>
              <Button
                type='button'
                variant='outline'
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Issue'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
