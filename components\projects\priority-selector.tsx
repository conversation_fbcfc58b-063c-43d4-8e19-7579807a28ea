'use client';

import { CheckIcon } from 'lucide-react';
import { useId, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { usePriorities } from '@/hooks/use-db';
import { getPriorityIcon, PriorityIcon } from '@/lib/constants/priorities';

interface PriorityOption {
  id: string;
  name: string;
  icon_name: string;
  sort_order: number;
}

interface PrioritySelectorProps {
  priority: PriorityOption | null;
  onPriorityChange?: (priorityId: string) => void;
}

export function PrioritySelector({
  priority,
  onPriorityChange,
}: PrioritySelectorProps) {
  const id = useId();
  const [open, setOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>(priority?.id || '');
  const { priorities: priorityOptions, loading } = usePriorities();
  // Load priority options from API

  const handlePriorityChange = (priorityId: string) => {
    setValue(priorityId);
    setOpen(false);

    if (onPriorityChange) {
      onPriorityChange(priorityId);
    }
  };

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id={id}
            className='flex items-center justify-center'
            size='icon_s'
            variant='ghost'
            // role='combobox'
            aria-expanded={open}
          >
            {priority ? (
              <span className='text-muted-foreground text-sm'>
                <PriorityIcon PriorityName={priority.id} />
              </span>
            ) : (
              <span className='text-muted-foreground text-sm'>
                {loading ? '...' : '—'}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className='border-input w-48 p-0' align='start'>
          <Command>
            <CommandInput placeholder='Set priority...' />
            <CommandList>
              <CommandEmpty>No priority found.</CommandEmpty>
              <CommandGroup>
                {loading ? (
                  <CommandItem disabled>
                    <span>Loading priorities...</span>
                  </CommandItem>
                ) : (
                  priorityOptions.map((item) => (
                    <CommandItem
                      key={item.id}
                      value={item.id}
                      onSelect={handlePriorityChange}
                      className='flex items-center justify-between'
                    >
                      <div className='flex items-center gap-2'>
                        <span className='text-muted-foreground text-sm'>
                          {(() => {
                            const IconComponent = getPriorityIcon(
                              item.name.toLowerCase()
                            );
                            return IconComponent ? (
                              <IconComponent className='size-4' />
                            ) : null;
                          })()}
                        </span>
                        <span className='text-xs'>{item.name}</span>
                      </div>
                      {(value === item.id || priority?.id === item.id) && (
                        <CheckIcon size={14} className='ml-auto' />
                      )}
                    </CommandItem>
                  ))
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
