# Auth Profile Creation Implementation

## Overview

This implementation provides automatic profile creation for new users when they sign up through Supabase Auth. The system looks up user data in the `JOIN_US_TABLE` based on email and creates a comprehensive profile with the appropriate role and information.

## Database Functions Created

### 1. `create_profile_for_user(user_id, user_email, user_metadata)`

**Purpose**: Creates a profile row for a new user by looking up data in `JOIN_US_TABLE`.

**Parameters**:
- `user_id` (UUID): The authenticated user's ID from `auth.users`
- `user_email` (TEXT): The user's email address
- `user_metadata` (JSONB): Optional metadata from auth signup (default: `{}`)

**Returns**: JSONB object with operation result

**Logic**:
1. Checks if profile already exists (prevents duplicates)
2. Looks up user data in `JOIN_US_TABLE` by email
3. Determines full name from:
   - `JOIN_US_TABLE.full_name` (priority 1)
   - `user_metadata.full_name` (priority 2)
   - `user_metadata.name` (priority 3)
   - Email prefix as fallback (priority 4)
4. Determines role from:
   - `JOIN_US_TABLE.join_role` if found
   - `'Volunteer'` as default for users not in `JOIN_US_TABLE`
5. Creates profile with all relevant data

### 2. `handle_new_user_signup()`

**Purpose**: Trigger function for automatic profile creation.

**Usage**: Can be called manually or through Supabase auth hooks.

### 3. `create_missing_profiles()`

**Purpose**: Utility function to create profiles for existing auth users who don't have profiles.

**Returns**: Table with results for each user processed.

## Implementation Details

### Profile Data Mapping

| Profile Field | Data Source | Fallback |
|---------------|-------------|----------|
| `id` | `auth.users.id` | - |
| `email` | `auth.users.email` | - |
| `name` | `JOIN_US_TABLE.full_name` | Email prefix |
| `full_name` | `JOIN_US_TABLE.full_name` | Email prefix |
| `role` | `JOIN_US_TABLE.join_role` | `'Volunteer'` |
| `users_id` | `JOIN_US_TABLE.id` | `NULL` |
| `created_at` | `NOW()` | - |
| `joined_date` | `NOW()` | - |
| `status` | `'offline'` | - |

### Role Assignment Logic

```sql
-- If user exists in JOIN_US_TABLE
role = JOIN_US_TABLE.join_role  -- Can be: 'Admin', 'Collaborator', 'Affiliate', 'Volunteer'

-- If user NOT in JOIN_US_TABLE
role = 'Volunteer'  -- Default role
```

## Usage Instructions

### Method 1: Client-Side Integration (Recommended)

Add this to your auth signup flow in your application:

```typescript
// After successful user signup
const { data: authData, error: authError } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'password',
  options: {
    data: {
      full_name: 'User Full Name'
    }
  }
});

if (authData.user && !authError) {
  // Call the profile creation function
  const { data: profileResult, error: profileError } = await supabase
    .rpc('create_profile_for_user', {
      user_id: authData.user.id,
      user_email: authData.user.email,
      user_metadata: authData.user.user_metadata || {}
    });

  if (profileError) {
    console.error('Error creating profile:', profileError);
  } else {
    console.log('Profile creation result:', profileResult);
  }
}
```

### Method 2: Supabase Auth Hooks (Advanced)

You can set up a Supabase Auth Hook to automatically call the function:

1. Go to your Supabase Dashboard
2. Navigate to Authentication > Hooks
3. Create a new hook for "Sign up" event
4. Use this SQL:

```sql
SELECT public.create_profile_for_user(
  NEW.id,
  NEW.email,
  COALESCE(NEW.raw_user_meta_data, '{}'::jsonb)
);
```

### Method 3: Batch Processing for Existing Users

To create profiles for existing users who don't have them:

```sql
-- Run this in Supabase SQL Editor
SELECT * FROM public.create_missing_profiles();
```

## Testing the Implementation

### Test Profile Creation Function

```sql
-- Test with a user email that exists in JOIN_US_TABLE
SELECT public.create_profile_for_user(
  'test-user-id'::uuid,
  '<EMAIL>',
  '{"full_name": "Test User"}'::jsonb
);

-- Test with a user email that doesn't exist in JOIN_US_TABLE
SELECT public.create_profile_for_user(
  'test-user-id-2'::uuid,
  '<EMAIL>',
  '{"full_name": "New User"}'::jsonb
);
```

### Verify Profile Creation

```sql
-- Check created profiles
SELECT 
  p.id,
  p.email,
  p.name,
  p.role,
  p.users_id,
  j.full_name as join_us_full_name,
  j.join_role as join_us_role
FROM profiles p
LEFT JOIN "JOIN_US_TABLE" j ON p.users_id = j.id
WHERE p.created_at > NOW() - INTERVAL '1 hour'
ORDER BY p.created_at DESC;
```

## Error Handling

The functions include comprehensive error handling:

- **Duplicate Prevention**: Checks if profile already exists
- **Graceful Degradation**: Creates basic profile even if `JOIN_US_TABLE` lookup fails
- **Detailed Error Messages**: Returns specific error information
- **Transaction Safety**: Uses exception handling to prevent data corruption

## Security Considerations

- Functions use `SECURITY DEFINER` to run with elevated privileges
- Proper permissions granted to `authenticated` and `anon` roles
- Input validation and sanitization included
- No sensitive data exposed in error messages

## Monitoring and Maintenance

### Check Function Performance

```sql
-- Monitor profile creation success rate
SELECT 
  COUNT(*) as total_profiles,
  COUNT(users_id) as profiles_with_join_us_data,
  ROUND(COUNT(users_id)::numeric / COUNT(*) * 100, 2) as join_us_match_percentage
FROM profiles
WHERE created_at > NOW() - INTERVAL '30 days';
```

### Common Issues and Solutions

1. **Profile Not Created**: Check if function was called after signup
2. **Wrong Role Assigned**: Verify email exists in `JOIN_US_TABLE` with correct `join_role`
3. **Missing Data**: Check `JOIN_US_TABLE` data completeness

## Integration with Existing Codebase

The implementation integrates seamlessly with your existing:
- [`authService`](lib/supabase/auth/auth-service.ts) - Add profile creation call after signup
- [`userStore`](lib/store/user.ts) - Profile data will be available immediately
- [`middleware`](lib/supabase/auth/middleware.ts) - No changes needed, works with existing RLS policies

## Next Steps

1. **Test the implementation** with your signup flow
2. **Add client-side integration** to your auth service
3. **Run batch processing** for existing users if needed
4. **Monitor profile creation** success rates
5. **Consider adding auth hooks** for fully automatic operation

The implementation is now ready for production use and will automatically create profiles with the appropriate role and data from your `JOIN_US_TABLE`.