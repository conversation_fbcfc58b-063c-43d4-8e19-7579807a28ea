import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { TeamMemberAdded } from '@/lib/emails/teams/member-added';
import type { TeamMemberAddedEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve, reject) => {
    let emailData: TeamMemberAddedEmailData;

    request
      .json()
      .then((data: TeamMemberAddedEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.team ||
          !emailData.member ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: team, member, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = TeamMemberAdded(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(TeamMemberAdded(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Welcome to ${emailData.team.name}`,
          react: TeamMemberAdded(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Team member added email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Welcome to ${emailData.team.name}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Team member added email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error: error.message || 'Failed to send team member added email',
          })
        );
      });
  });
}
