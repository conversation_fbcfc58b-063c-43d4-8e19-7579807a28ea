'use client';

import {
  Building2,
  Calendar,
  Mail,
  MapPin,
  Phone,
  TrendingUp,
} from 'lucide-react';
import { useMemo } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import type { Client } from '@/lib/supabase/database-modules';

interface ClientsMetricsProps {
  clients: Client[];
  loading?: boolean;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  icon?: React.ReactNode;
  loading?: boolean;
}

function MetricCard({
  title,
  value,
  subtitle,
  variant = 'default',
  icon,
  loading = false,
}: MetricCardProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'destructive':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      default:
        return 'border-border bg-card';
    }
  };

  if (loading) {
    return (
      <Card className={getVariantStyles()}>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>
            <Skeleton className='h-4 w-24' />
          </CardTitle>
          <Skeleton className='h-4 w-4' />
        </CardHeader>
        <CardContent>
          <Skeleton className='h-8 w-16 mb-1' />
          <Skeleton className='h-3 w-32' />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={getVariantStyles()}>
      <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
        <CardTitle className='text-sm font-medium'>{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className='text-2xl font-bold'>{value}</div>
        <p className='text-xs text-muted-foreground'>{subtitle}</p>
      </CardContent>
    </Card>
  );
}

export function ClientsMetrics({
  clients,
  loading = false,
}: ClientsMetricsProps) {
  const metrics = useMemo(() => {
    if (loading) {
      return Array(6)
        .fill(null)
        .map((_, index) => ({
          title: '',
          value: '',
          subtitle: '',
          icon: null,
          variant: 'default' as const,
          loading: true,
        }));
    }

    // Calculate time-based metrics
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const recentClients = clients.filter(
      (c) => new Date(c.created_at) > thirtyDaysAgo
    ).length;

    const thisWeekClients = clients.filter(
      (c) => new Date(c.created_at) > sevenDaysAgo
    ).length;

    // Calculate contact information completeness
    const clientsWithEmail = clients.filter(
      (c) => c.email && c.email.trim() !== ''
    ).length;
    const clientsWithPhone = clients.filter(
      (c) => c.phone && c.phone.trim() !== ''
    ).length;
    const clientsWithAddress = clients.filter(
      (c) => c.address && c.address !== null
    ).length;

    // Calculate active clients (those with projects)
    const activeClients = clients.filter((c) => c.is_active !== false).length;

    return [
      {
        title: 'Total Clients',
        value: clients.length,
        subtitle: `${recentClients} added this month`,
        icon: <Building2 className='h-4 w-4 text-blue-600' />,
        variant: 'default' as const,
        loading: false,
      },
      {
        title: 'New This Week',
        value: thisWeekClients,
        subtitle: 'Recently added clients',
        icon: <TrendingUp className='h-4 w-4 text-green-600' />,
        variant: thisWeekClients > 0 ? 'success' : ('default' as const),
        loading: false,
      },
      {
        title: 'Active Clients',
        value: activeClients,
        subtitle: `${((activeClients / clients.length) * 100).toFixed(1)}% of total`,
        icon: <Calendar className='h-4 w-4 text-orange-600' />,
        variant: 'default' as const,
        loading: false,
      },
      {
        title: 'With Email',
        value: clientsWithEmail,
        subtitle: `${((clientsWithEmail / clients.length) * 100).toFixed(1)}% complete`,
        icon: <Mail className='h-4 w-4 text-purple-600' />,
        variant:
          clientsWithEmail / clients.length > 0.8
            ? 'success'
            : ('warning' as const),
        loading: false,
      },
      {
        title: 'With Phone',
        value: clientsWithPhone,
        subtitle: `${((clientsWithPhone / clients.length) * 100).toFixed(1)}% complete`,
        icon: <Phone className='h-4 w-4 text-indigo-600' />,
        variant:
          clientsWithPhone / clients.length > 0.6
            ? 'success'
            : ('warning' as const),
        loading: false,
      },
      {
        title: 'With Address',
        value: clientsWithAddress,
        subtitle: `${((clientsWithAddress / clients.length) * 100).toFixed(1)}% complete`,
        icon: <MapPin className='h-4 w-4 text-emerald-600' />,
        variant:
          clientsWithAddress / clients.length > 0.4
            ? 'success'
            : ('warning' as const),
        loading: false,
      },
    ];
  }, [clients, loading]);

  return (
    <div className='space-y-4'>
      <div>
        <h2 className='text-lg font-semibold'>Clients Overview</h2>
        <p className='text-sm text-muted-foreground'>
          Statistics and insights for client records and contact information
        </p>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4'>
        {metrics.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            subtitle={metric.subtitle}
            variant={
              (metric.variant as
                | 'default'
                | 'success'
                | 'warning'
                | 'destructive') || 'default'
            }
            icon={metric.icon}
            loading={metric.loading}
          />
        ))}
      </div>
    </div>
  );
}
