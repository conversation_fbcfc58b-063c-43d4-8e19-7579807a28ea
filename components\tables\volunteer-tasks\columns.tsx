'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Eye, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

import { DatePicker } from '@/components/issues/date-picker';
import { StatusSelector } from '@/components/issues/status-selector';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTasks } from '@/hooks/use-db';
import { PriorityIcon } from '@/lib/constants/priorities';
import type { Task } from '@/lib/supabase/database-modules';

export const columns: ColumnDef<Task>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) => {
      const task = row.original;
      return (
        <div className='flex items-center gap-2'>
          <div className='flex flex-col items-start overflow-hidden'>
            <Link
              href={`/volunteer/tasks/${task.id}`}
              className='font-medium truncate w-full hover:underline'
            >
              {task.title}
            </Link>
            {task.description && (
              <p className='text-xs text-muted-foreground truncate w-full'>
                {task.description}
              </p>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'project',
    header: 'Project',
    cell: ({ row }) => {
      const task = row.original;
      const project = task.project;

      if (!project) {
        return <span className='text-muted-foreground'>No project</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <span className='font-medium'>{project.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const task = row.original;

      return (
        <StatusSelector status={task.status || null} issueId={task.id} />
      );
    },
  },
  {
    accessorKey: 'priority',
    header: 'Priority',
    cell: ({ row }) => {
      const task = row.original;
      const priority = task.priority;

      if (!priority) {
        return <span className='text-muted-foreground'>No priority set</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          {priority.icon_name && (
            <PriorityIcon PriorityName={priority.icon_name} />
          )}
          <span className='text-sm font-medium'>{priority.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'assignee',
    header: 'Assignee',
    cell: ({ row }) => {
      const task = row.original;
      const assignee = task.assignee;

      if (!assignee) {
        return <span className='text-muted-foreground'>Unassigned</span>;
      }

      return (
        <div className='flex items-center gap-2'>
          <Avatar className='size-6'>
            <AvatarImage
              src={assignee.avatar_url || undefined}
              alt={assignee.name || assignee.email}
            />
            <AvatarFallback>
              {assignee.email[0] || 'UN'.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm font-medium'>{assignee.name}</span>
        </div>
      );
    },
  },
  {
    accessorKey: 'due_date',
    header: 'Due Date',
    cell: ({ row }) => {
      const task = row.original;
      const { updateTask } = useTasks();

      const handleDateChange = (date: Date | null) => {
        const dateString = date ? date.toISOString() : null;
        updateTask(task.id, { due_date: dateString })
          .then(() => {
            toast.success('Task due date updated');
          })
          .catch((error) => {
            toast.error('Failed to update task due date');
            console.error('Error updating task due date:', error);
          });
      };

      return (
        <DatePicker
          date={task.due_date ? new Date(task.due_date) : null}
          onDateChange={handleDateChange}
        />
      );
    },
  },
  {
    accessorKey: 'progress',
    header: 'Progress',
    cell: ({ row }) => {
      const task = row.original;
      const progress = task.progress || 0;

      const getProgressColor = (progress: number) => {
        if (progress >= 100) return 'bg-green-500';
        if (progress >= 75) return 'bg-blue-500';
        if (progress >= 50) return 'bg-yellow-500';
        if (progress >= 25) return 'bg-orange-500';
        return 'bg-red-500';
      };

      return (
        <div className='flex items-center gap-2'>
          <div className='w-16 h-2 bg-muted rounded-full overflow-hidden'>
            <div 
              className={`h-full transition-all duration-300 ${getProgressColor(progress)}`}
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <span className='text-xs text-muted-foreground min-w-[3rem]'>
            {progress}%
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const task = row.original;
      const createdDate = new Date(task.created_at);

      return (
        <span className='text-sm text-muted-foreground'>
          {createdDate.toLocaleDateString()}
        </span>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const task = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='w-64'>
            <DropdownMenuItem asChild>
              <Link href={`/volunteer/tasks/${task.id}`}>
                <Eye className='mr-2 h-4 w-4' />
                View Task
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
