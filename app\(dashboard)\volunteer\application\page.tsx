'use client';

import { Calendar, CheckCircle, Clock, FileText, Send } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useProfile } from '@/hooks/use-db';

interface ApplicationStatus {
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  notes?: string;
}

interface VolunteerApplication {
  id: string;
  skills: string[];
  availability: string;
  experience: string;
  motivation: string;
  preferredProjects: string[];
  status: ApplicationStatus;
}

export default function VolunteerApplicationPage() {
  const { profile } = useProfile();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock application data - in real implementation, this would come from database
  const [application, setApplication] = useState<VolunteerApplication>({
    id: '1',
    skills: ['React', 'TypeScript', 'Node.js', 'UI/UX Design'],
    availability: '10-15 hours per week, weekends preferred',
    experience: 'I have 3 years of experience in web development and have contributed to several open-source projects. I\'m passionate about using technology to solve real-world problems.',
    motivation: 'I want to give back to the community and use my technical skills to help organizations that are making a positive impact. Volunteering allows me to grow professionally while contributing to meaningful causes.',
    preferredProjects: ['Web Development', 'Mobile Apps', 'Data Analysis', 'UI/UX Design'],
    status: {
      status: 'approved',
      submittedAt: '2024-01-15T10:00:00Z',
      reviewedAt: '2024-01-18T14:30:00Z',
      reviewedBy: 'Admin Team',
      notes: 'Excellent technical background and clear motivation. Approved for web development projects.',
    },
  });

  const [formData, setFormData] = useState({
    skills: application.skills.join(', '),
    availability: application.availability,
    experience: application.experience,
    motivation: application.motivation,
    preferredProjects: application.preferredProjects.join(', '),
  });

  const handleSubmitUpdate = () => {
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      const updatedApplication = {
        ...application,
        skills: formData.skills.split(',').map(s => s.trim()),
        availability: formData.availability,
        experience: formData.experience,
        motivation: formData.motivation,
        preferredProjects: formData.preferredProjects.split(',').map(s => s.trim()),
        status: {
          ...application.status,
          status: 'under_review' as const,
        },
      };
      
      setApplication(updatedApplication);
      setIsSubmitting(false);
      toast.success('Application updated successfully');
    }, 1500);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'pending':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className='h-4 w-4' />;
      case 'under_review':
        return <Clock className='h-4 w-4' />;
      case 'pending':
        return <FileText className='h-4 w-4' />;
      default:
        return <FileText className='h-4 w-4' />;
    }
  };

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Volunteer Application</p>
      </header>
      
      <ScrollArea className='flex-1'>
        <section className='p-6 space-y-6'>
          {/* Application Status */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                Application Status
                <Badge className={getStatusColor(application.status.status)}>
                  <div className='flex items-center gap-1'>
                    {getStatusIcon(application.status.status)}
                    {application.status.status.replace('_', ' ').toUpperCase()}
                  </div>
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <Label className='text-sm font-medium'>Submitted</Label>
                  <div className='flex items-center gap-2 mt-1'>
                    <Calendar className='h-4 w-4 text-muted-foreground' />
                    <span className='text-sm'>
                      {new Date(application.status.submittedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                {application.status.reviewedAt && (
                  <div>
                    <Label className='text-sm font-medium'>Reviewed</Label>
                    <div className='flex items-center gap-2 mt-1'>
                      <Calendar className='h-4 w-4 text-muted-foreground' />
                      <span className='text-sm'>
                        {new Date(application.status.reviewedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              
              {application.status.reviewedBy && (
                <div>
                  <Label className='text-sm font-medium'>Reviewed By</Label>
                  <p className='text-sm text-muted-foreground mt-1'>
                    {application.status.reviewedBy}
                  </p>
                </div>
              )}
              
              {application.status.notes && (
                <div>
                  <Label className='text-sm font-medium'>Review Notes</Label>
                  <p className='text-sm text-muted-foreground mt-1 p-3 bg-muted rounded-lg'>
                    {application.status.notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Application Form */}
          <Card>
            <CardHeader>
              <CardTitle>Application Details</CardTitle>
              <p className='text-sm text-muted-foreground'>
                Update your volunteer application information below.
              </p>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div>
                <Label htmlFor='skills'>Technical Skills</Label>
                <Input
                  id='skills'
                  value={formData.skills}
                  onChange={(e) => setFormData(prev => ({ ...prev, skills: e.target.value }))}
                  placeholder='React, TypeScript, Node.js, etc.'
                  className='mt-2'
                />
                <p className='text-xs text-muted-foreground mt-1'>
                  Separate skills with commas
                </p>
              </div>

              <div>
                <Label htmlFor='availability'>Availability</Label>
                <Input
                  id='availability'
                  value={formData.availability}
                  onChange={(e) => setFormData(prev => ({ ...prev, availability: e.target.value }))}
                  placeholder='e.g., 10-15 hours per week, weekends preferred'
                  className='mt-2'
                />
              </div>

              <div>
                <Label htmlFor='experience'>Relevant Experience</Label>
                <Textarea
                  id='experience'
                  value={formData.experience}
                  onChange={(e) => setFormData(prev => ({ ...prev, experience: e.target.value }))}
                  placeholder='Describe your relevant experience and background...'
                  className='mt-2 min-h-[120px]'
                />
              </div>

              <div>
                <Label htmlFor='motivation'>Motivation for Volunteering</Label>
                <Textarea
                  id='motivation'
                  value={formData.motivation}
                  onChange={(e) => setFormData(prev => ({ ...prev, motivation: e.target.value }))}
                  placeholder='Why do you want to volunteer with us?'
                  className='mt-2 min-h-[120px]'
                />
              </div>

              <div>
                <Label htmlFor='preferredProjects'>Preferred Project Types</Label>
                <Input
                  id='preferredProjects'
                  value={formData.preferredProjects}
                  onChange={(e) => setFormData(prev => ({ ...prev, preferredProjects: e.target.value }))}
                  placeholder='Web Development, Mobile Apps, Data Analysis, etc.'
                  className='mt-2'
                />
                <p className='text-xs text-muted-foreground mt-1'>
                  Separate project types with commas
                </p>
              </div>

              <Separator />

              <div className='flex justify-end'>
                <Button 
                  onClick={handleSubmitUpdate}
                  disabled={isSubmitting}
                  className='flex items-center gap-2'
                >
                  <Send className='h-4 w-4' />
                  {isSubmitting ? 'Updating...' : 'Update Application'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Application Guidelines */}
          <Card>
            <CardHeader>
              <CardTitle>Application Guidelines</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-3'>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Be specific about your skills:</strong> List the technologies, tools, and methodologies you're comfortable with.
                  </p>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Realistic availability:</strong> Be honest about the time you can commit to volunteering.
                  </p>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Show your passion:</strong> Explain why you want to volunteer and what motivates you.
                  </p>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Keep it updated:</strong> Update your application as your skills and availability change.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </ScrollArea>
    </main>
  );
}
