'use client';

import { Award, Clock, FolderOpen, Target, TrendingUp, Users } from 'lucide-react';
import { useEffect } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useVolunteerMetrics, useProfile } from '@/hooks/use-db';

interface VolunteerMetricsProps {
  className?: string;
}

export function VolunteerMetrics({ className }: VolunteerMetricsProps) {
  const { profile } = useProfile();
  const { metrics, fetchVolunteerMetrics } = useVolunteerMetrics();

  useEffect(() => {
    if (profile?.id) {
      fetchVolunteerMetrics(profile.id).catch(console.error);
    }
  }, [profile?.id, fetchVolunteerMetrics]);

  const getContributionLevel = (score: number) => {
    if (score >= 800) return { level: 'Expert', color: 'text-purple-600', bgColor: 'bg-purple-100' };
    if (score >= 600) return { level: 'Advanced', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (score >= 400) return { level: 'Intermediate', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (score >= 200) return { level: 'Beginner', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { level: 'New', color: 'text-gray-600', bgColor: 'bg-gray-100' };
  };

  const contributionLevel = getContributionLevel(metrics.contributionScore);

  if (metrics.loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <Skeleton className='h-4 w-20' />
              <Skeleton className='h-4 w-4' />
            </CardHeader>
            <CardContent>
              <Skeleton className='h-8 w-16 mb-2' />
              <Skeleton className='h-3 w-24' />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {/* Total Projects */}
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Total Projects</CardTitle>
          <FolderOpen className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{metrics.totalProjects}</div>
          <p className='text-xs text-muted-foreground'>
            {metrics.activeProjects} currently active
          </p>
        </CardContent>
      </Card>

      {/* Completed Issues */}
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Issues Resolved</CardTitle>
          <Target className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{metrics.completedIssues}</div>
          <p className='text-xs text-muted-foreground'>
            Issues completed successfully
          </p>
        </CardContent>
      </Card>

      {/* Volunteer Hours */}
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Hours Contributed</CardTitle>
          <Clock className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='text-2xl font-bold'>{metrics.totalHours}</div>
          <p className='text-xs text-muted-foreground'>
            Estimated volunteer time
          </p>
        </CardContent>
      </Card>

      {/* Contribution Score */}
      <Card>
        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
          <CardTitle className='text-sm font-medium'>Contribution Level</CardTitle>
          <Award className='h-4 w-4 text-muted-foreground' />
        </CardHeader>
        <CardContent>
          <div className='flex items-center gap-2 mb-2'>
            <div className='text-2xl font-bold'>{metrics.contributionScore}</div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${contributionLevel.bgColor} ${contributionLevel.color}`}>
              {contributionLevel.level}
            </div>
          </div>
          <div className='space-y-1'>
            <Progress value={(metrics.contributionScore / 1000) * 100} className='h-2' />
            <p className='text-xs text-muted-foreground'>
              {1000 - metrics.contributionScore} points to next level
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Detailed volunteer statistics component
export function VolunteerStatsCard() {
  const { profile } = useProfile();
  const { metrics, fetchVolunteerMetrics } = useVolunteerMetrics();

  useEffect(() => {
    if (profile?.id) {
      fetchVolunteerMetrics(profile.id).catch(console.error);
    }
  }, [profile?.id, fetchVolunteerMetrics]);

  if (metrics.loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <TrendingUp className='h-5 w-5' />
            Volunteer Statistics
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className='flex items-center justify-between'>
              <Skeleton className='h-4 w-32' />
              <Skeleton className='h-4 w-16' />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  const stats = [
    {
      label: 'Projects Contributed To',
      value: metrics.totalProjects,
      icon: FolderOpen,
      color: 'text-blue-600',
    },
    {
      label: 'Active Projects',
      value: metrics.activeProjects,
      icon: Users,
      color: 'text-green-600',
    },
    {
      label: 'Issues Resolved',
      value: metrics.completedIssues,
      icon: Target,
      color: 'text-purple-600',
    },
    {
      label: 'Hours Volunteered',
      value: `${metrics.totalHours}h`,
      icon: Clock,
      color: 'text-orange-600',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <TrendingUp className='h-5 w-5' />
          Volunteer Statistics
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {stats.map((stat, index) => (
          <div key={index} className='flex items-center justify-between p-3 border rounded-lg'>
            <div className='flex items-center gap-3'>
              <div className={`p-2 rounded-full bg-muted ${stat.color}`}>
                <stat.icon className='h-4 w-4' />
              </div>
              <span className='font-medium'>{stat.label}</span>
            </div>
            <span className='text-lg font-bold'>{stat.value}</span>
          </div>
        ))}
        
        <div className='mt-6 p-4 bg-muted/50 rounded-lg'>
          <div className='flex items-center justify-between mb-2'>
            <span className='font-medium'>Contribution Score</span>
            <span className='text-xl font-bold'>{metrics.contributionScore}/1000</span>
          </div>
          <Progress value={(metrics.contributionScore / 1000) * 100} className='h-3' />
          <p className='text-xs text-muted-foreground mt-2'>
            Keep contributing to increase your score and unlock new opportunities!
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
