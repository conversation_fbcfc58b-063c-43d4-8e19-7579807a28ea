import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import type { ProjectMemberAddedEmailData } from '@/lib/types/email-types';

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const ProjectMemberAdded = ({
  project,
  member,
  triggeredBy,
  role,
}: ProjectMemberAddedEmailData) => (
  <Html>
    <Head />
    <Preview>You've been added to {project.name}</Preview>
    <Body style={main}>
      {/* Header Section */}
      <Container
        style={{
          ...container,
          backgroundColor: colors['100'],
        }}
      >
        <Img
          src={`${baseUrl}/thehuefactory_hero.png`}
          width='100%'
          height='auto'
          alt='Email Header Image'
        />
      </Container>

      {/* Header Transition */}
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['100'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        ></Section>
      </Container>

      {/* Main Content */}
      <Container style={container}>
        <Heading style={h1}>Welcome to the Team!</Heading>

        <Text style={{ ...text, marginBottom: '24px' }}>
          Hi <strong>{member.full_name}</strong>,
        </Text>

        <Text style={{ ...text, marginBottom: '24px' }}>
          Great news! You've been added to the <strong>{project.name}</strong>{' '}
          project by {triggeredBy.full_name}. You can now collaborate on tasks
          and track your progress.
        </Text>

        {/* Project Details */}
        <Container style={projectCard}>
          <Heading style={projectTitle}>{project.name}</Heading>

          {project.description && (
            <Text style={{ ...text, marginBottom: '16px' }}>
              {project.description}
            </Text>
          )}

          <Container style={detailsContainer}>
            <Text style={detailLabel}>Your Role:</Text>
            <Text style={detailValue}>{role || 'Team Member'}</Text>
          </Container>

          {project.lead && (
            <Container style={detailsContainer}>
              <Text style={detailLabel}>Project Lead:</Text>
              <Text style={detailValue}>{project.lead.full_name}</Text>
            </Container>
          )}

          {project.status && (
            <Container style={detailsContainer}>
              <Text style={detailLabel}>Status:</Text>
              <Container
                style={{
                  ...statusBadge,
                  backgroundColor: project.status.color || '#28a745',
                }}
              >
                <Text style={statusText}>{project.status.name}</Text>
              </Container>
            </Container>
          )}

          {project.priority && (
            <Container style={detailsContainer}>
              <Text style={detailLabel}>Priority:</Text>
              <Text style={detailValue}>{project.priority.name}</Text>
            </Container>
          )}

          {project.target_date && (
            <Container style={detailsContainer}>
              <Text style={detailLabel}>Target Date:</Text>
              <Text style={detailValue}>
                {new Date(project.target_date).toLocaleDateString()}
              </Text>
            </Container>
          )}

          {project.percent_complete !== undefined && (
            <Container style={detailsContainer}>
              <Text style={detailLabel}>Progress:</Text>
              <Text style={detailValue}>
                {project.percent_complete}% Complete
              </Text>
            </Container>
          )}

          <Container style={detailsContainer}>
            <Text style={detailLabel}>Added by:</Text>
            <Text style={detailValue}>{triggeredBy.full_name}</Text>
          </Container>
        </Container>

        {/* Action Button */}
        <Container style={{ textAlign: 'center', marginTop: '32px' }}>
          <Link
            href='https://team.thehuefactory.co/dashboard'
            target='_blank'
            style={{
              backgroundColor: colors['100'],
              paddingRight: 18,
              paddingLeft: 18,
              fontWeight: 'bold',
              color: 'white',
              paddingTop: 16,
              paddingBottom: 16,
              borderRadius: 32,
              whiteSpace: 'nowrap',
              fontFamily: 'monospace',
              textDecoration: 'none',
              display: 'inline-block',
            }}
          >
            View Project Dashboard
          </Link>
        </Container>

        <Text style={{ ...text, marginBottom: '24px', marginTop: '32px' }}>
          As a project member, you'll receive notifications about project
          updates, new issues, and important milestones. You can manage your
          notification preferences in your{' '}
          <Link
            href='https://team.thehuefactory.co/login'
            target='_blank'
            style={{ ...link, color: colors['100'], fontWeight: 'bold' }}
          >
            account settings
          </Link>
          .
        </Text>

        <Text style={{ ...text, marginBottom: '24px' }}>
          If you have any questions about the project or need help getting
          started, feel free to reach out to{' '}
          {project.lead?.full_name || 'the project team'} or{' '}
          <Link
            href='https://www.thehuefactory.co/contact'
            target='_blank'
            style={{ ...link, color: colors['100'], fontWeight: 'bold' }}
          >
            contact us
          </Link>{' '}
          for support.
        </Text>
      </Container>

      {/* Footer Section */}
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
          height='42'
          alt="thehuefactory's Logo"
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            target='_blank'
            style={{ ...link, color: colors['100'], fontWeight: 'bold' }}
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2024 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default ProjectMemberAdded;

// Styles following existing pattern
const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const projectCard = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  padding: '24px',
  marginBottom: '24px',
};

const projectTitle = {
  color: colors['100'],
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '20px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
  padding: '0',
};

const detailsContainer = {
  display: 'flex',
  marginBottom: '8px',
  alignItems: 'center',
};

const detailLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0',
  minWidth: '120px',
};

const detailValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '0',
};

const statusBadge = {
  display: 'inline-block',
  padding: '6px 12px',
  borderRadius: '16px',
  color: 'white',
  fontWeight: 'bold',
  fontSize: '12px',
  marginLeft: '8px',
};

const statusText = {
  color: 'white',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  fontWeight: 'bold',
  margin: '0',
};
