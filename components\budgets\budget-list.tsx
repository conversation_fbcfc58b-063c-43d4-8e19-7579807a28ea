'use client';

import { 
  Eye, 
  Edit, 
  Trash2, 
  Plus, 
  Filter, 
  Search,
  DollarSign,
  Calendar,
  User,
  Building
} from 'lucide-react';
import { useState, useMemo } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { BudgetForm } from './budget-form';
import { BudgetDisplay } from '@/components/projects/budget-display';
import { useBudgets, useProjects, useClients } from '@/hooks/use-db';
import type { Budget } from '@/lib/supabase/database-modules';
import { formatCurrency } from '@/lib/utils/format-currency';
import { cn } from '@/lib/utils/cn';

interface BudgetListProps {
  projectId?: string;
  clientId?: string;
  affiliateId?: string;
  className?: string;
}

export function BudgetList({ projectId, clientId, affiliateId, className }: BudgetListProps) {
  const { budgets, deleteBudget } = useBudgets();
  const { projects } = useProjects();
  const { clients } = useClients();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedBudget, setSelectedBudget] = useState<Budget | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Filter budgets based on props and filters
  const filteredBudgets = useMemo(() => {
    let filtered = budgets;

    // Filter by props
    if (projectId) {
      filtered = filtered.filter(b => b.ProjectId === projectId);
    }
    if (clientId) {
      filtered = filtered.filter(b => b.ClientId === clientId);
    }
    if (affiliateId) {
      filtered = filtered.filter(b => b.affiliateId === affiliateId);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(budget => {
        const project = projects.find(p => p.id === budget.ProjectId);
        const client = clients.find(c => c.id === budget.ClientId);
        
        return (
          project?.name.toLowerCase().includes(term) ||
          client?.name.toLowerCase().includes(term) ||
          budget.Category.toLowerCase().includes(term) ||
          budget.Notes?.toLowerCase().includes(term)
        );
      });
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(b => b.Status === statusFilter);
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(b => b.Category === categoryFilter);
    }

    return filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  }, [budgets, projects, clients, projectId, clientId, affiliateId, searchTerm, statusFilter, categoryFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'locked':
        return 'bg-yellow-100 text-yellow-800';
      case 'spent':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'marketing':
        return 'bg-blue-100 text-blue-800';
      case 'development':
        return 'bg-purple-100 text-purple-800';
      case 'consulting':
        return 'bg-orange-100 text-orange-800';
      case 'operations':
        return 'bg-green-100 text-green-800';
      case 'other':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDelete = async () => {
    if (!selectedBudget) return;

    try {
      await deleteBudget(selectedBudget.id);
      toast.success('Budget deleted successfully');
      setIsDeleteDialogOpen(false);
      setSelectedBudget(null);
    } catch (error) {
      console.error('Error deleting budget:', error);
      toast.error(
        `Failed to delete budget: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  };

  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project?.name || 'Unknown Project';
  };

  const getClientName = (clientId: string | null) => {
    if (!clientId) return 'No Client';
    const client = clients.find(c => c.id === clientId);
    return client?.name || 'Unknown Client';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const calculateSpentPercentage = (budget: Budget) => {
    const spent = budget.ActualAmount - budget.CurrentAmount;
    return (spent / budget.ActualAmount) * 100;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Budget Management
            </CardTitle>
            <CardDescription>
              {projectId ? 'Project budgets' : 
               clientId ? 'Client budgets' : 
               affiliateId ? 'Affiliate budgets' : 
               'All budgets'}
            </CardDescription>
          </div>
          <Button 
            onClick={() => setIsCreateDialogOpen(true)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Budget
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search budgets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="locked">Locked</SelectItem>
                <SelectItem value="spent">Spent</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="development">Development</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
                <SelectItem value="consulting">Consulting</SelectItem>
                <SelectItem value="operations">Operations</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Budget Table */}
        {filteredBudgets.length > 0 ? (
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Project</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Spent</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead className="w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredBudgets.map((budget) => {
                  const spentPercentage = calculateSpentPercentage(budget);
                  return (
                    <TableRow key={budget.id}>
                      <TableCell>
                        <div className="font-medium">
                          {getProjectName(budget.ProjectId)}
                        </div>
                        {budget.has_affiliate && (
                          <div className="text-xs text-muted-foreground">
                            Affiliate Project
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          {getClientName(budget.ClientId)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {formatCurrency(budget.ActualAmount, budget.Currency)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Remaining: {formatCurrency(budget.CurrentAmount, budget.Currency)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm font-medium">
                            {spentPercentage.toFixed(1)}%
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={cn(
                                "h-2 rounded-full transition-all",
                                spentPercentage > 80 ? "bg-red-500" :
                                spentPercentage > 60 ? "bg-yellow-500" :
                                "bg-green-500"
                              )}
                              style={{ width: `${Math.min(spentPercentage, 100)}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(budget.Status)}>
                          {budget.Status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getCategoryColor(budget.Category)}>
                          {budget.Category}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{formatDate(budget.StartDate)}</div>
                          <div className="text-muted-foreground">
                            to {formatDate(budget.EndDate)}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => {
                              setSelectedBudget(budget);
                              setIsViewDialogOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0"
                            onClick={() => {
                              setSelectedBudget(budget);
                              setIsEditDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            onClick={() => {
                              setSelectedBudget(budget);
                              setIsDeleteDialogOpen(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No budgets found</p>
            <p className="text-sm">
              {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all'
                ? 'Try adjusting your filters'
                : 'Create your first budget to get started'
              }
            </p>
          </div>
        )}
      </CardContent>

      {/* Create Budget Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Budget</DialogTitle>
            <DialogDescription>
              Set up a new budget for project tracking and expense management.
            </DialogDescription>
          </DialogHeader>
          <BudgetForm
            projectId={projectId}
            clientId={clientId}
            onSuccess={() => {
              setIsCreateDialogOpen(false);
              toast.success('Budget created successfully');
            }}
            onCancel={() => setIsCreateDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Budget Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Budget</DialogTitle>
            <DialogDescription>
              Update budget details and allocations.
            </DialogDescription>
          </DialogHeader>
          {selectedBudget && (
            <BudgetForm
              budget={selectedBudget}
              onSuccess={() => {
                setIsEditDialogOpen(false);
                setSelectedBudget(null);
                toast.success('Budget updated successfully');
              }}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedBudget(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Budget Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Budget Details</DialogTitle>
            <DialogDescription>
              Comprehensive view of budget information and status.
            </DialogDescription>
          </DialogHeader>
          {selectedBudget && (
            <BudgetDisplay budget={selectedBudget} />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Budget</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this budget? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedBudget && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="text-sm font-medium mb-2">Budget to Delete</div>
                <div className="text-sm space-y-1">
                  <div>Project: {getProjectName(selectedBudget.ProjectId)}</div>
                  <div>Amount: {formatCurrency(selectedBudget.ActualAmount, selectedBudget.Currency)}</div>
                  <div>Status: {selectedBudget.Status}</div>
                </div>
              </div>
              <div className="flex gap-3">
                <Button
                  variant="destructive"
                  onClick={handleDelete}
                  className="flex-1"
                >
                  Delete Budget
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDeleteDialogOpen(false);
                    setSelectedBudget(null);
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
