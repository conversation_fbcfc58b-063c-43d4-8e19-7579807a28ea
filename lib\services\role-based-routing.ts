import type { Database } from '@/lib/supabase/database-types';

type UserRole = Database['public']['Enums']['Role Types'];

// Base URL for the application
const BASE_URL = 'https://team.thehuefactory.co';

// Role-based routing service using object pattern instead of class
const roleBasedRouting = {
  // Get the appropriate dashboard URL based on user role
  getDashboardUrl: (role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return `${BASE_URL}/admin`;
      case 'collaborator':
        return `${BASE_URL}/collaborator`;
      case 'volunteer':
        return `${BASE_URL}/volunteer`;
      case 'affiliate':
        return `${BASE_URL}/affiliate`;
      default:
        return `${BASE_URL}/dashboard`;
    }
  },

  // Get the appropriate project URL based on user role
  getProjectUrl: (projectId: string, role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return `${BASE_URL}/admin/projects/${projectId}`;
      case 'collaborator':
        return `${BASE_URL}/collaborator/projects/${projectId}`;
      case 'volunteer':
        return `${BASE_URL}/volunteer/projects/${projectId}`;
      case 'affiliate':
        return `${BASE_URL}/affiliate/projects/${projectId}`;
      default:
        return `${BASE_URL}/projects/${projectId}`;
    }
  },

  // Get the appropriate project issues URL based on user role
  getProjectIssuesUrl: (projectId: string, role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return `${BASE_URL}/admin/projects/${projectId}/issues`;
      case 'collaborator':
        return `${BASE_URL}/collaborator/projects/${projectId}/tasks`;
      case 'volunteer':
        return `${BASE_URL}/volunteer/projects/${projectId}/tasks`;
      case 'affiliate':
        return `${BASE_URL}/affiliate/projects/${projectId}`;
      default:
        return `${BASE_URL}/projects/${projectId}/issues`;
    }
  },

  // Get the appropriate projects list URL based on user role
  getProjectsListUrl: (role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return `${BASE_URL}/admin/projects`;
      case 'collaborator':
        return `${BASE_URL}/collaborator/projects`;
      case 'volunteer':
        return `${BASE_URL}/volunteer/projects`;
      case 'affiliate':
        return `${BASE_URL}/affiliate/projects`;
      default:
        return `${BASE_URL}/projects`;
    }
  },

  // Get the appropriate profile/settings URL based on user role
  getProfileUrl: (role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return `${BASE_URL}/admin/profile`;
      case 'collaborator':
        return `${BASE_URL}/collaborator/dashboard/profile`;
      case 'volunteer':
        return `${BASE_URL}/volunteer/profile`;
      case 'affiliate':
        return `${BASE_URL}/affiliate/profile`;
      default:
        return `${BASE_URL}/profile`;
    }
  },

  // Get role-specific welcome message for emails
  getRoleWelcomeMessage: (role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'You have full administrative access to manage this project.';
      case 'collaborator':
        return 'You can now collaborate on tasks and track your progress.';
      case 'volunteer':
        return 'Thank you for volunteering! You can now contribute to this project.';
      case 'affiliate':
        return 'You can now track this project and manage referrals.';
      default:
        return 'You can now access this project.';
    }
  },

  // Get role-specific action button text
  getRoleActionText: (
    role: UserRole
  ): { primary: string; secondary: string } => {
    switch (role.toLowerCase()) {
      case 'admin':
        return {
          primary: 'Manage Project',
          secondary: 'View Issues',
        };
      case 'collaborator':
        return {
          primary: 'View Project',
          secondary: 'View Tasks',
        };
      case 'volunteer':
        return {
          primary: 'View Project',
          secondary: 'View Tasks',
        };
      case 'affiliate':
        return {
          primary: 'View Project',
          secondary: 'Track Progress',
        };
      default:
        return {
          primary: 'View Project',
          secondary: 'View Details',
        };
    }
  },

  // Validate if a role has access to a specific feature
  hasFeatureAccess: (role: UserRole, feature: string): boolean => {
    const rolePermissions: Record<string, string[]> = {
      admin: ['manage', 'edit', 'delete', 'view', 'assign', 'approve'],
      collaborator: ['view', 'edit_tasks', 'submit', 'comment'],
      volunteer: ['view', 'contribute', 'comment'],
      affiliate: ['view', 'track', 'refer'],
    };

    const permissions = rolePermissions[role.toLowerCase()] || [];
    return permissions.includes(feature);
  },

  // Get appropriate notification preferences URL
  getNotificationSettingsUrl: (role: UserRole): string => {
    switch (role.toLowerCase()) {
      case 'admin':
        return `${BASE_URL}/admin/settings/notifications`;
      case 'collaborator':
        return `${BASE_URL}/collaborator/dashboard/settings`;
      case 'volunteer':
        return `${BASE_URL}/volunteer/settings`;
      case 'affiliate':
        return `${BASE_URL}/affiliate/settings`;
      default:
        return `${BASE_URL}/settings/notifications`;
    }
  },
};

export { roleBasedRouting };
export type { UserRole };
