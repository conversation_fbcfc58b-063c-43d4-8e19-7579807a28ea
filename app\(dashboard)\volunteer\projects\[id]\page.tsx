'use client';

import { ArrowLef<PERSON>, Calendar, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { StatusIcon } from '@/components/icons/status-icon';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useProjects, useMembers } from '@/hooks/use-db';
import type { Project } from '@/lib/supabase/database-modules';

interface ProjectDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function VolunteerProjectDetailPage({ params }: ProjectDetailPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);
  const { getProjectById, updateProject } = useProjects();
  const { members } = useMembers();

  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);
  const [progressNotes, setProgressNotes] = useState('');
  const [isUpdatingProgress, setIsUpdatingProgress] = useState(false);

  const projectId = resolvedParams.id;

  // Fetch project data
  useEffect(() => {
    if (projectId) {
      setLoading(true);
      getProjectById(projectId)
        .then((fetchedProject) => {
          if (fetchedProject) {
            setProject(fetchedProject);
            // Initialize progress notes if they exist
            setProgressNotes(fetchedProject.progress_notes || '');
          } else {
            setNotFound(true);
          }
        })
        .catch((error) => {
          console.error('Error fetching project:', error);
          toast.error('Failed to load project');
          setNotFound(true);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [projectId, getProjectById]);

  const handleUpdateProgress = () => {
    if (!project) return;

    setIsUpdatingProgress(true);
    updateProject(project.id, { progress_notes: progressNotes })
      .then(() => {
        toast.success('Progress notes updated successfully');
        // Refresh project data
        return getProjectById(project.id);
      })
      .then((updatedProject) => {
        if (updatedProject) {
          setProject(updatedProject);
        }
      })
      .catch((error) => {
        console.error('Error updating progress:', error);
        toast.error('Failed to update progress notes');
      })
      .finally(() => {
        setIsUpdatingProgress(false);
      });
  };

  if (loading) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Loading Project...</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-muted-foreground'>Loading project details...</div>
          </div>
        </section>
      </main>
    );
  }

  if (notFound || !project) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <div className='flex items-center gap-4'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => router.back()}
              className='h-8 w-8 p-0'
            >
              <ArrowLeft className='h-4 w-4' />
            </Button>
            <p className='font-medium text-sm'>Project Not Found</p>
          </div>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-64'>
            <div className='text-center space-y-4'>
              <div className='text-destructive'>Project not found</div>
              <Button onClick={() => router.push('/volunteer/projects')}>
                Go to Projects
              </Button>
            </div>
          </div>
        </section>
      </main>
    );
  }

  const creator = members.find(m => m.id === project.created_by);
  const displayName = project.name || 'Untitled Project';

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={() => router.back()}
            className='h-8 w-8 p-0'
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <p className='font-medium text-sm'>{displayName}</p>
        </div>
      </header>
      <ScrollArea className='flex-1'>
        <section className='p-6 space-y-6'>
          <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
            {/* Main Content */}
            <div className='lg:col-span-2 space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>{displayName}</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label className='text-sm font-medium'>Description</Label>
                    <div className='mt-2 text-sm text-muted-foreground whitespace-pre-wrap'>
                      {project.description || 'No description provided'}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Progress Notes - Volunteers can edit this */}
              <Card>
                <CardHeader>
                  <CardTitle className='text-base'>Progress Notes</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <Textarea
                    value={progressNotes}
                    onChange={(e) => setProgressNotes(e.target.value)}
                    className='min-h-[120px]'
                    placeholder='Add your progress notes here...'
                  />
                  <Button 
                    onClick={handleUpdateProgress}
                    disabled={isUpdatingProgress}
                    size='sm'
                  >
                    {isUpdatingProgress ? 'Updating...' : 'Update Progress'}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className='space-y-6'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-base'>Project Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div>
                    <Label className='text-sm font-medium'>Status</Label>
                    <div className='mt-2 flex items-center gap-2'>
                      {project.status && (
                        <>
                          <StatusIcon status={project.status.name} />
                          <span className='text-sm font-medium'>{project.status.name}</span>
                        </>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className='text-sm font-medium'>Priority</Label>
                    <div className='mt-2'>
                      {project.priority ? (
                        <Badge variant='outline'>{project.priority.name}</Badge>
                      ) : (
                        <span className='text-sm text-muted-foreground'>No priority set</span>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className='text-sm font-medium'>Project Lead</Label>
                    <div className='mt-2'>
                      {project.lead ? (
                        <div className='flex items-center gap-2'>
                          <Avatar className='h-6 w-6'>
                            <AvatarImage 
                              src={project.lead.avatar_url || ''} 
                              alt={project.lead.full_name || 'Lead'} 
                            />
                            <AvatarFallback>
                              {project.lead.full_name?.split(' ').map(n => n[0]).join('') || 'L'}
                            </AvatarFallback>
                          </Avatar>
                          <span className='text-sm'>{project.lead.full_name}</span>
                        </div>
                      ) : (
                        <span className='text-sm text-muted-foreground'>No lead assigned</span>
                      )}
                    </div>
                  </div>

                  {project.client && (
                    <div>
                      <Label className='text-sm font-medium'>Client</Label>
                      <div className='mt-2'>
                        <Badge variant='outline'>{project.client.name}</Badge>
                      </div>
                    </div>
                  )}

                  <div>
                    <Label className='text-sm font-medium'>Due Date</Label>
                    <div className='mt-2 text-sm text-muted-foreground'>
                      {project.due_date ? (
                        <div className='flex items-center gap-2'>
                          <Calendar className='h-4 w-4' />
                          {new Date(project.due_date).toLocaleDateString()}
                        </div>
                      ) : (
                        'No due date set'
                      )}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <Label className='text-sm font-medium'>Created</Label>
                    <div className='mt-2 text-sm text-muted-foreground'>
                      {new Date(project.created_at).toLocaleDateString()} by{' '}
                      {creator?.full_name || 'Unknown'}
                    </div>
                  </div>

                  <div>
                    <Label className='text-sm font-medium'>Updated</Label>
                    <div className='mt-2 text-sm text-muted-foreground'>
                      {new Date(project.updated_at).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Team Members */}
              {project.members && project.members.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className='text-base flex items-center gap-2'>
                      <Users className='h-4 w-4' />
                      Team Members ({project.members.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-3'>
                      {project.members.map((member) => (
                        <div key={member.id} className='flex items-center gap-2'>
                          <Avatar className='h-6 w-6'>
                            <AvatarImage 
                              src={member.avatar_url || ''} 
                              alt={member.full_name || 'Member'} 
                            />
                            <AvatarFallback>
                              {member.full_name?.split(' ').map(n => n[0]).join('') || 'M'}
                            </AvatarFallback>
                          </Avatar>
                          <span className='text-sm'>{member.full_name}</span>
                          <Badge variant='secondary' className='text-xs'>
                            {member.role}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </section>
      </ScrollArea>
    </main>
  );
}
