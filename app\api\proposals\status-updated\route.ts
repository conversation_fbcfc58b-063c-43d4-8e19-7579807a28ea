import { pretty, render } from '@react-email/render';
import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ProposalStatusUpdate } from '@/lib/emails/proposals/status-updated';
import type { ProposalStatusUpdateEmailData } from '@/lib/types/email-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

export async function POST(request: Request): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve, reject) => {
    let emailData: ProposalStatusUpdateEmailData;

    request
      .json()
      .then((data: ProposalStatusUpdateEmailData) => {
        emailData = data;

        // Validate required data
        if (
          !emailData.proposal ||
          !emailData.oldStatus ||
          !emailData.newStatus ||
          !emailData.recipients ||
          emailData.recipients.length === 0
        ) {
          throw new Error(
            'Missing required email data: proposal, oldStatus, newStatus, or recipients'
          );
        }

        // Create email template with data
        const emailTemplate = ProposalStatusUpdate(emailData);

        // Render email with enhanced formatting
        return render(emailTemplate);
      })
      .then((rendered) => {
        return Promise.all([
          pretty(rendered),
          render(ProposalStatusUpdate(emailData), { plainText: true }),
        ]);
      })
      .then(([html, text]) => {
        return resend.emails.send({
          from: 'Thehuefactory <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: emailData.recipients,
          bcc: ['<EMAIL>'],
          subject: `Proposal Status Updated: ${emailData.proposal.title}`,
          react: ProposalStatusUpdate(emailData),
          html: html,
          text: text,
        });
      })
      .then((emailResult) => {
        console.log('Proposal status update email sent successfully:', {
          id: emailResult.data?.id,
          subject: `Proposal Status Updated: ${emailData.proposal.title}`,
          recipients: emailData.recipients,
        });
        resolve(
          NextResponse.json({
            success: true,
            data: emailResult,
            messageId: emailResult.data?.id,
          })
        );
      })
      .catch((error) => {
        console.error('Proposal status update email error:', error);
        reject(
          NextResponse.json({
            success: false,
            error:
              error.message || 'Failed to send proposal status update email',
          })
        );
      });
  });
}
