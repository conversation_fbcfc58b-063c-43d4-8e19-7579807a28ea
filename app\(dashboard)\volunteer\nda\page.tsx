'use client';

import { CheckCircle, Download, FileText, Shield } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useProfile } from '@/hooks/use-db';

interface NDAStatus {
  signed: boolean;
  signedAt?: string;
  version: string;
  documentUrl?: string;
}

export default function VolunteerNDAPage() {
  const { profile } = useProfile();
  const [isAccepting, setIsAccepting] = useState(false);
  const [hasReadAgreement, setHasReadAgreement] = useState(false);
  const [hasUnderstoodTerms, setHasUnderstoodTerms] = useState(false);

  // Mock NDA status - in real implementation, this would come from database
  const [ndaStatus, setNdaStatus] = useState<NDAStatus>({
    signed: false,
    version: '2024.1',
    documentUrl: '/documents/volunteer-nda-2024.pdf',
  });

  const handleAcceptNDA = () => {
    if (!hasReadAgreement || !hasUnderstoodTerms) {
      toast.error('Please confirm that you have read and understood the agreement');
      return;
    }

    setIsAccepting(true);
    
    // Simulate API call
    setTimeout(() => {
      setNdaStatus({
        ...ndaStatus,
        signed: true,
        signedAt: new Date().toISOString(),
      });
      setIsAccepting(false);
      toast.success('NDA agreement signed successfully');
    }, 2000);
  };

  const handleDownloadNDA = () => {
    // In real implementation, this would download the actual NDA document
    toast.success('NDA document download started');
  };

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <div className='flex items-center gap-2'>
          <Shield className='h-4 w-4' />
          <p className='font-medium text-sm'>NDA Agreement</p>
        </div>
        {ndaStatus.signed && (
          <Badge className='bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'>
            <CheckCircle className='h-3 w-3 mr-1' />
            Signed
          </Badge>
        )}
      </header>
      
      <ScrollArea className='flex-1'>
        <section className='p-6 space-y-6'>
          {/* NDA Status */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <FileText className='h-5 w-5' />
                Non-Disclosure Agreement Status
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex items-center justify-between p-4 border rounded-lg'>
                <div>
                  <p className='font-medium'>
                    {ndaStatus.signed ? 'Agreement Signed' : 'Agreement Pending'}
                  </p>
                  <p className='text-sm text-muted-foreground'>
                    Version {ndaStatus.version}
                    {ndaStatus.signedAt && (
                      <span> • Signed on {new Date(ndaStatus.signedAt).toLocaleDateString()}</span>
                    )}
                  </p>
                </div>
                <div className='flex items-center gap-2'>
                  <Button variant='outline' size='sm' onClick={handleDownloadNDA}>
                    <Download className='h-4 w-4 mr-2' />
                    Download
                  </Button>
                  {ndaStatus.signed ? (
                    <Badge className='bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'>
                      <CheckCircle className='h-3 w-3 mr-1' />
                      Signed
                    </Badge>
                  ) : (
                    <Badge variant='outline'>
                      Pending
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* NDA Content */}
          <Card>
            <CardHeader>
              <CardTitle>Volunteer Non-Disclosure Agreement</CardTitle>
              <p className='text-sm text-muted-foreground'>
                Please read the following agreement carefully before proceeding.
              </p>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='prose prose-sm max-w-none dark:prose-invert'>
                <h3>1. Purpose</h3>
                <p>
                  This Non-Disclosure Agreement ("Agreement") is entered into between TheHueFactory ("Company") 
                  and the volunteer ("Volunteer") to protect confidential and proprietary information that may 
                  be disclosed during the volunteer engagement.
                </p>

                <h3>2. Confidential Information</h3>
                <p>
                  Confidential Information includes, but is not limited to:
                </p>
                <ul>
                  <li>Client information, data, and project details</li>
                  <li>Business strategies, plans, and methodologies</li>
                  <li>Technical specifications, code, and documentation</li>
                  <li>Financial information and pricing structures</li>
                  <li>Any information marked as confidential or proprietary</li>
                </ul>

                <h3>3. Obligations</h3>
                <p>
                  The Volunteer agrees to:
                </p>
                <ul>
                  <li>Keep all Confidential Information strictly confidential</li>
                  <li>Not disclose Confidential Information to any third party</li>
                  <li>Use Confidential Information solely for volunteer activities</li>
                  <li>Return or destroy all Confidential Information upon request</li>
                  <li>Notify the Company immediately of any unauthorized disclosure</li>
                </ul>

                <h3>4. Duration</h3>
                <p>
                  This Agreement remains in effect during the volunteer engagement and continues 
                  for a period of two (2) years after the termination of the volunteer relationship.
                </p>

                <h3>5. Exceptions</h3>
                <p>
                  This Agreement does not apply to information that:
                </p>
                <ul>
                  <li>Is publicly available through no breach of this Agreement</li>
                  <li>Was known to the Volunteer prior to disclosure</li>
                  <li>Is independently developed without use of Confidential Information</li>
                  <li>Is required to be disclosed by law or court order</li>
                </ul>

                <h3>6. Remedies</h3>
                <p>
                  The Volunteer acknowledges that any breach of this Agreement may cause irreparable 
                  harm to the Company, and the Company shall be entitled to seek injunctive relief 
                  and other legal remedies.
                </p>

                <h3>7. Governing Law</h3>
                <p>
                  This Agreement shall be governed by and construed in accordance with the laws 
                  of the jurisdiction where the Company is located.
                </p>
              </div>

              <Separator />

              {!ndaStatus.signed && (
                <div className='space-y-4'>
                  <div className='space-y-3'>
                    <div className='flex items-center space-x-2'>
                      <Checkbox 
                        id='read-agreement'
                        checked={hasReadAgreement}
                        onCheckedChange={(checked) => setHasReadAgreement(checked as boolean)}
                      />
                      <Label htmlFor='read-agreement' className='text-sm'>
                        I have read and understood the entire Non-Disclosure Agreement
                      </Label>
                    </div>
                    
                    <div className='flex items-center space-x-2'>
                      <Checkbox 
                        id='understand-terms'
                        checked={hasUnderstoodTerms}
                        onCheckedChange={(checked) => setHasUnderstoodTerms(checked as boolean)}
                      />
                      <Label htmlFor='understand-terms' className='text-sm'>
                        I understand my obligations and agree to comply with all terms
                      </Label>
                    </div>
                  </div>

                  <div className='flex justify-end pt-4'>
                    <Button 
                      onClick={handleAcceptNDA}
                      disabled={!hasReadAgreement || !hasUnderstoodTerms || isAccepting}
                      className='flex items-center gap-2'
                    >
                      <Shield className='h-4 w-4' />
                      {isAccepting ? 'Signing Agreement...' : 'Sign NDA Agreement'}
                    </Button>
                  </div>
                </div>
              )}

              {ndaStatus.signed && (
                <div className='flex items-center justify-center p-6 bg-green-50 dark:bg-green-900/20 rounded-lg'>
                  <div className='text-center space-y-2'>
                    <CheckCircle className='h-12 w-12 text-green-600 mx-auto' />
                    <p className='font-medium text-green-800 dark:text-green-300'>
                      NDA Agreement Signed Successfully
                    </p>
                    <p className='text-sm text-green-600 dark:text-green-400'>
                      Signed on {ndaStatus.signedAt && new Date(ndaStatus.signedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Important Notes */}
          <Card>
            <CardHeader>
              <CardTitle className='text-base'>Important Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Required for Access:</strong> Signing this NDA is required to access confidential project information and client data.
                  </p>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Legal Binding:</strong> This is a legally binding agreement. Please read carefully before signing.
                  </p>
                </div>
                <div className='flex items-start gap-3'>
                  <div className='w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0' />
                  <p className='text-sm text-muted-foreground'>
                    <strong>Questions:</strong> If you have any questions about this agreement, please contact the admin team before signing.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </ScrollArea>
    </main>
  );
}
