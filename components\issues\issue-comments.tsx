'use client';

import { MessageSquare, Send } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { useIssues, useProfile } from '@/hooks/use-db';

interface Comment {
  id: string;
  content: string;
  created_at: string;
  created_by: string;
  author?: {
    id: string;
    full_name: string;
    email: string;
    avatar_url?: string;
  };
}

interface IssueCommentsProps {
  issueId: string;
  comments?: Comment[];
  onCommentAdded?: () => void;
}

export function IssueComments({ issueId, comments = [], onCommentAdded }: IssueCommentsProps) {
  const { addIssueComment } = useIssues();
  const { profile } = useProfile();
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitComment = () => {
    if (!newComment.trim() || !profile?.id) {
      toast.error('Please enter a comment');
      return;
    }

    setIsSubmitting(true);
    addIssueComment(issueId, newComment, profile.id)
      .then(() => {
        toast.success('Comment added successfully');
        setNewComment('');
        onCommentAdded?.();
      })
      .catch((error) => {
        console.error('Error adding comment:', error);
        toast.error('Failed to add comment');
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmitComment();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <MessageSquare className='h-5 w-5' />
          Comments ({comments.length})
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {/* Add Comment Form */}
        <div className='space-y-3'>
          <div className='flex items-start gap-3'>
            <Avatar className='h-8 w-8'>
              <AvatarImage 
                src={profile?.avatar_url || ''} 
                alt={profile?.full_name || 'User'} 
              />
              <AvatarFallback>
                {profile?.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className='flex-1 space-y-2'>
              <Textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder='Add a comment... (Ctrl+Enter to submit)'
                className='min-h-[80px] resize-none'
              />
              <div className='flex justify-between items-center'>
                <p className='text-xs text-muted-foreground'>
                  Press Ctrl+Enter to submit
                </p>
                <Button 
                  size='sm' 
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim() || isSubmitting}
                >
                  <Send className='h-4 w-4 mr-2' />
                  {isSubmitting ? 'Adding...' : 'Comment'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Comments List */}
        {comments.length > 0 ? (
          <ScrollArea className='max-h-[400px]'>
            <div className='space-y-4'>
              {comments.map((comment) => (
                <div key={comment.id} className='flex items-start gap-3 p-3 border rounded-lg'>
                  <Avatar className='h-8 w-8'>
                    <AvatarImage 
                      src={comment.author?.avatar_url || ''} 
                      alt={comment.author?.full_name || 'User'} 
                    />
                    <AvatarFallback>
                      {comment.author?.full_name?.split(' ').map(n => n[0]).join('') || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className='flex-1 space-y-1'>
                    <div className='flex items-center gap-2'>
                      <p className='text-sm font-medium'>
                        {comment.author?.full_name || 'Unknown User'}
                      </p>
                      <p className='text-xs text-muted-foreground'>
                        {new Date(comment.created_at).toLocaleDateString()} at{' '}
                        {new Date(comment.created_at).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </p>
                    </div>
                    <p className='text-sm text-muted-foreground whitespace-pre-wrap'>
                      {comment.content}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className='text-center py-8 text-muted-foreground'>
            <MessageSquare className='h-12 w-12 mx-auto mb-3 opacity-50' />
            <p className='text-sm'>No comments yet</p>
            <p className='text-xs'>Be the first to add a comment!</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
