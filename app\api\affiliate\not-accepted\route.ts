import { NextResponse } from 'next/server';
import { Resend } from 'resend';
import { ApplicationNotAccepted } from '@/lib/emails/not-accepted';
import type { Database } from '@/lib/supabase/database-types';

const resend = new Resend(process.env.NEXT_PUBLIC_RESEND_API_KEY || '');

type dataProps = Database['public']['Tables']['JOIN_US_TABLE']['Row'];

export async function POST(request: Request) {
  const form: dataProps = await request.json();
  console.log('start');
  try {
    const data = await resend.emails.send({
      from: 'Thehuefactory <<EMAIL>>',
      replyTo: '<EMAIL>',
      to: form.email ? [form.email] : [],
      bcc: ['<EMAIL>', '<EMAIL>'],
      subject: 'Your Application Has Not Been Accepted.',
      text: '',
      react: ApplicationNotAccepted(form),
    });
    return NextResponse.json({ data });
  } catch (error) {
    console.log(error);
    return NextResponse.json({ error });
  }
}
