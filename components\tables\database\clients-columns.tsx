'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Edit, Eye, MoreHorizontal, Trash2 } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import type { Client } from '@/lib/supabase/database-modules';

interface ColumnsProps {
  onViewClient: (client: Client) => void;
  onEditClient: (client: Client) => void;
  onDeleteClient: (client: Client) => void;
  onStatusChange: (clientId: string, isActive: boolean) => void;
}

export const createColumns = ({
  onViewClient,
  onEditClient,
  onDeleteClient,
  onStatusChange,
}: ColumnsProps): ColumnDef<Client>[] => [
  {
    accessorKey: 'name',
    header: 'Client Name',
    cell: ({ row }) => {
      const client = row.original;
      return (
        <div className='flex flex-col'>
          <span className='font-medium'>{client.name || 'Unknown'}</span>
          {client.company_name && (
            <span className='text-xs text-muted-foreground'>
              {client.company_name}
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      const email = row.getValue('email') as string;
      return email ? (
        <a
          href={`mailto:${email}`}
          className='text-blue-600 hover:underline text-sm'
        >
          {email}
        </a>
      ) : (
        <span className='text-muted-foreground text-sm'>No email</span>
      );
    },
  },
  {
    accessorKey: 'phone',
    header: 'Phone',
    cell: ({ row }) => {
      const phone = row.getValue('phone') as string;
      return phone ? (
        <a
          href={`tel:${phone}`}
          className='text-blue-600 hover:underline text-sm'
        >
          {phone}
        </a>
      ) : (
        <span className='text-muted-foreground text-sm'>No phone</span>
      );
    },
  },
  {
    accessorKey: 'industry',
    header: 'Industry',
    cell: ({ row }) => {
      const industry = row.getValue('industry') as string;
      return industry ? (
        <Badge variant='outline' className='text-xs'>
          {industry}
        </Badge>
      ) : (
        <span className='text-muted-foreground text-sm'>Not specified</span>
      );
    },
  },
  {
    accessorKey: 'is_active',
    header: 'Status',
    cell: ({ row }) => {
      const client = row.original;
      const isActive = client.is_active !== false; // Default to true if null/undefined

      return (
        <div className='flex items-center space-x-2'>
          <Switch
            checked={isActive}
            onCheckedChange={(checked) => onStatusChange(client.id, checked)}
          />
          <span className='text-xs text-muted-foreground'>
            {isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const createdAt = row.getValue('created_at') as string;

      if (!createdAt) {
        return <span className='text-muted-foreground text-sm'>Unknown</span>;
      }

      const date = new Date(createdAt);
      if (Number.isNaN(date.getTime())) {
        return (
          <span className='text-muted-foreground text-sm'>Invalid Date</span>
        );
      }

      return (
        <div className='text-sm'>
          <div>{format(date, 'MMM dd, yyyy')}</div>
          <div className='text-xs text-muted-foreground'>
            {format(date, 'HH:mm')}
          </div>
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const client = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onViewClient(client)}>
              <Eye className='mr-2 h-4 w-4' />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEditClient(client)}>
              <Edit className='mr-2 h-4 w-4' />
              Edit Client
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => onDeleteClient(client)}
              className='text-destructive'
            >
              <Trash2 className='mr-2 h-4 w-4' />
              Delete Client
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
