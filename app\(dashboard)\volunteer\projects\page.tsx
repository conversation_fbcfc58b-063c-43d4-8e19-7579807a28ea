'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { useProjects, useProfile } from '@/hooks/use-db';
import type { Project } from '@/lib/supabase/database-modules';

// Create volunteer-specific columns (similar to collaborator columns but for volunteers)
import { columns } from '@/components/tables/volunteer-projects/columns';

export default function VolunteerProjectsPage() {
  const { getCollaboratorProjects } = useProjects();
  const { profile } = useProfile();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!profile?.id) return;

    setLoading(true);
    setError(null);

    // Use the same method as collaborators since volunteers have similar access patterns
    getCollaboratorProjects(profile.id)
      .then((volunteerProjects) => {
        setProjects(volunteerProjects);
      })
      .catch((error) => {
        console.error('Error fetching volunteer projects:', error);
        setError('Failed to load projects');
        toast.error('Failed to load projects');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [profile?.id, getCollaboratorProjects]);

  if (error) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <p className='font-medium text-sm'>Projects</p>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-32'>
            <p className='text-muted-foreground'>{error}</p>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Projects</p>
      </header>
      <section className='flex-1 p-6'>
        <DataTable columns={columns} data={projects} isLoading={loading} />
      </section>
    </main>
  );
}
