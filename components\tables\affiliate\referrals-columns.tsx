'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { Edit, Eye, MoreHorizontal } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Proposal } from '@/lib/supabase/database-modules';

interface ColumnsProps {
  onViewReferral: (proposal: Proposal) => void;
  onEditReferral: (proposal: Proposal) => void;
}

const getClientName = (proposal: Proposal): string => {
  if (proposal.client_name) {
    return proposal.client_name;
  }
  if (proposal.affiliate_proposal?.client_name) {
    return proposal.affiliate_proposal.client_name;
  }
  return 'Unknown Client';
};

const getStatusBadge = (proposal: Proposal) => {
  if (proposal.is_approved === true) {
    return (
      <Badge
        variant='default'
        className='bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      >
        Approved
      </Badge>
    );
  }
  if (proposal.is_approved === false) {
    return <Badge variant='destructive'>Rejected</Badge>;
  }
  return <Badge variant='secondary'>Pending</Badge>;
};

export const createReferralsColumns = ({
  onViewReferral,
  onEditReferral,
}: ColumnsProps): ColumnDef<Proposal>[] => [
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: ({ row }) => {
      const createdAt = row.getValue('created_at') as string;

      if (!createdAt) {
        return <div className='font-medium text-muted-foreground'>Unknown</div>;
      }

      const date = new Date(createdAt);
      if (Number.isNaN(date.getTime())) {
        return (
          <div className='font-medium text-muted-foreground'>Invalid Date</div>
        );
      }

      return <div className='font-medium'>{format(date, 'MMM dd, yyyy')}</div>;
    },
  },
  {
    id: 'client_name',
    header: 'Client Name',
    cell: ({ row }) => {
      const proposal = row.original;
      return <div className='font-medium'>{getClientName(proposal)}</div>;
    },
  },
  {
    accessorKey: 'client_email',
    header: 'Client Email',
    cell: ({ row }) => {
      const proposal = row.original;
      const email = proposal.client_email;
      return <div className='text-muted-foreground'>{email || '—'}</div>;
    },
  },
  {
    id: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const proposal = row.original;
      return getStatusBadge(proposal);
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      const proposal = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>Open menu</span>
              <MoreHorizontal className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={() => onViewReferral(proposal)}>
              <Eye className='mr-2 h-4 w-4' />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onEditReferral(proposal)}>
              <Edit className='mr-2 h-4 w-4' />
              Edit Referral
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
