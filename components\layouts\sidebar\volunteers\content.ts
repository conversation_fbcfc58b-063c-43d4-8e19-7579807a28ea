import {
  Box,
  FileText,
  Home,
  Inbox,
  Layers,
  Shield,
  Tag,
  User,
  Zap,
} from 'lucide-react';
import type { SidebarData } from '@/lib/supabase/database-modules';
import type { Database } from '@/lib/supabase/database-types';

type Profile_Types = Database['public']['Tables']['profiles']['Row'];

const volunteerSidebarData = ({
  profile,
}: {
  profile: Profile_Types;
}): SidebarData => {
  const user = {
    name: profile.full_name,
    email: profile.email,
    avatar: '/avatars/volunteer.jpg',
    role: profile.role,
  };
  const teams = [
    {
      name: 'Volunteer Panel',
      logo: Shield,
      plan: 'Community',
    },
  ];
  const navHeader = [
    {
      title: 'Home',
      url: '/volunteer',
      icon: Home,
    },
    {
      title: 'Inbox',
      url: '/volunteer/inbox',
      icon: Inbox,
    },
  ];
  const navMain = [
    {
      title: 'workspace',
      items: [
        {
          title: 'Projects',
          url: '/volunteer/projects',
          icon: Box,
          isActive: true,
        },
        {
          title: 'Issues',
          url: '/volunteer/issues',
          icon: Layers,
          isActive: true,
        },
        {
          title: 'Tasks',
          url: '/volunteer/tasks',
          icon: Tag,
          isActive: true,
        },
      ],
    },
    {
      title: 'Profile',
      items: [
        {
          title: 'Portfolio',
          url: '/volunteer/portfolio',
          icon: User,
          isActive: true,
        },
        {
          title: 'Application',
          url: '/volunteer/application',
          icon: FileText,
          isActive: true,
        },
      ],
    },
    {
      title: 'Legal',
      items: [
        {
          title: 'NDA Agreement',
          url: '/volunteer/nda',
          icon: Shield,
          isActive: true,
        },
        {
          title: 'Volunteer Brief',
          url: '/volunteer/brief',
          icon: FileText,
          isActive: true,
        },
      ],
    },
  ];

  return {
    user,
    teams,
    navMain,
    navHeader,
  };
};

export default volunteerSidebarData;
