'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { DataTable } from '@/components/ui/data-table';
import { useIssues, useProfile } from '@/hooks/use-db';
import type { Issue } from '@/lib/supabase/database-modules';

// Create volunteer-specific columns (similar to collaborator columns but for volunteers)
import { columns } from '@/components/tables/volunteer-issues/columns';

export default function VolunteerIssuesPage() {
  const { getCollaboratorIssues } = useIssues();
  const { profile } = useProfile();
  const [issues, setIssues] = useState<Issue[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!profile?.id) return;

    setLoading(true);
    setError(null);

    // Use the same method as collaborators since volunteers have similar access patterns
    getCollaboratorIssues(profile.id)
      .then((volunteerIssues) => {
        setIssues(volunteerIssues);
      })
      .catch((error) => {
        console.error('Error fetching volunteer issues:', error);
        setError('Failed to load issues');
        toast.error('Failed to load issues');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [profile?.id, getCollaboratorIssues]);

  if (error) {
    return (
      <main className='h-full flex flex-col'>
        <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
          <p className='font-medium text-sm'>Issues</p>
        </header>
        <section className='flex-1 p-6'>
          <div className='flex items-center justify-center h-32'>
            <p className='text-muted-foreground'>{error}</p>
          </div>
        </section>
      </main>
    );
  }

  return (
    <main className='h-full flex flex-col'>
      <header className='pl-12 pr-6 h-11 inline-flex items-center justify-between border-b border-neutral-300 dark:border-neutral-800 w-full'>
        <p className='font-medium text-sm'>Issues</p>
      </header>
      <section className='flex-1 p-6'>
        <DataTable columns={columns} data={issues} isLoading={loading} />
      </section>
    </main>
  );
}
