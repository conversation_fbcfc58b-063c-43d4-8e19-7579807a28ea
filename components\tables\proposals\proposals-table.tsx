'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import { ViewProposalDialog } from '@/components/proposals/view-proposal-dialog';
import { DataTable } from '@/components/ui/data-table';
import {
  useProposals,
  useClients,
  useBudgets,
  useProjects,
} from '@/hooks/use-db';
import { ProposalProcessingService } from '@/lib/services/proposal-processing-service';
import type { Proposal } from '@/lib/supabase/database-modules';

import { createColumns } from './columns';

export function ProposalsTable() {
  const { proposals, loading, error, updateProposal } = useProposals();
  const { addClient, fetchClient } = useClients();
  const { addBudget } = useBudgets();
  const { addProject } = useProjects();
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(
    null
  );
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);

  const handleViewProposal = (proposal: Proposal) => {
    setSelectedProposal(proposal);
    setIsViewDialogOpen(true);
  };

  const handleApprovalChange = async (
    proposalId: number,
    approved: boolean | null
  ) => {
    const proposal = proposals.find((p) => p.id === proposalId);
    if (!proposal) {
      toast.error('Proposal not found');
      return;
    }

    const statusText =
      approved === true
        ? 'approved'
        : approved === false
          ? 'rejected'
          : 'pending';

    try {
      // First update the proposal status
      await updateProposal(proposalId, { is_approved: approved });

      // If approved, process the proposal to create client, project, and budget
      if (approved === true) {
        try {
          // Process the approved proposal using the service
          const result =
            await ProposalProcessingService.processApprovedProposal(proposal, {
              addClient,
              fetchClient,
              addProject,
              addBudget,
            });

          // Generate and log processing report
          const report = ProposalProcessingService.generateProcessingReport(
            proposal,
            result
          );
          console.log(report);

          if (result.success) {
            toast.success(
              `Proposal approved and processed successfully!\n${result.summary}`,
              { duration: 8000 }
            );

            // Show warnings if any
            if (result.warnings.length > 0) {
              toast.info(`Note: ${result.warnings.join(', ')}`, {
                duration: 5000,
              });
            }
          } else {
            toast.warning(
              `Proposal approved but automatic processing failed: ${result.errors.join(', ')}`,
              { duration: 8000 }
            );
          }
        } catch (processingError) {
          console.error('Error processing approved proposal:', processingError);
          toast.error(
            `Proposal approved but automatic processing failed: ${
              processingError instanceof Error
                ? processingError.message
                : 'Unknown error'
            }`,
            { duration: 8000 }
          );
        }
      } else {
        // For rejected or pending status
        toast.success(`Proposal ${statusText} successfully`);
      }
    } catch (error) {
      console.error('Error updating proposal:', error);
      toast.error(
        `Failed to update proposal: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  };

  const columns = createColumns({
    onViewProposal: handleViewProposal,
    onApprovalChange: handleApprovalChange,
  });

  if (error) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-destructive'>Error: {error}</div>
      </div>
    );
  }

  return (
    <>
      <DataTable columns={columns} data={proposals} isLoading={loading} />

      <ViewProposalDialog
        proposal={selectedProposal}
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      />
    </>
  );
}
